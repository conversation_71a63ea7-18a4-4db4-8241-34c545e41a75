# AI Studio 开发架构设计文档

## 文档信息
- **项目名称**：AI Studio - 本地AI助手桌面应用
- **文档版本**：v3.0 深度优化完整架构设计版
- **目标平台**：Windows 和 macOS 桌面应用（专为桌面端设计）
- **分辨率支持**：最小800×600，默认1200×800，无移动端适配
- **核心技术栈**：Vue3.5+ + Vite7.0+ + Tauri2.x + Rust + SQLite + ChromaDB + Candle + LLaMA.cpp + ONNX Runtime
- **样式技术**：Tailwind CSS + SCSS（专为桌面端优化，无其他平台适配）
- **主题系统**：深色/浅色主题切换功能完整实现（不考虑其他主题）
- **国际化支持**：中文/英文双语切换完整支持（不考虑其他语言）
- **文档状态**：基于源文档深度优化的零内容缺失完整架构设计版
- **创建日期**：2025年1月
- **基于源文档**：开发设计文档.txt (20,755行)
- **优化目标**：零内容缺失，完整技术方案，清晰架构设计，详细线性交互流程图
- **源文档行数**：20,755行
- **目标文档要求**：内容完整性≥源文档，结构清晰，逻辑明确，无歧义

---

## 📋 详细目录

### 🔍 更新版 AI Studio 开发架构设计文档目录（增强版）

#### **第一部分：项目概述与规划**
- 1.1 项目背景与需求分析  
- 1.2 技术栈选型与决策  
- 1.3 **增强架构图**（组件交互/数据流向图）  
- 1.4 核心功能特性  
- 1.5 **跨平台架构增强**  
  - Windows/macOS适配方案  
  - Tauri硬件加速优化矩阵  
  - 平台特定功能封装层  

#### **第二部分：前端架构设计**
- 2.1 前端目录结构详解  
- 2.2 Vue3组件设计规范  
- 2.3 Tailwind CSS + SCSS样式方案  
- 2.4 **界面状态机设计**（UI状态转换图）  
- 2.5 状态管理与路由设计  

#### **第三部分：后端架构设计**
- 3.1 Rust后端目录结构  
- 3.2 Tauri集成与命令系统  
- 3.3 **AI推理引擎增强**  
  - 本地/云端推理切换协议  
  - 多引擎调度时序图  
  - 量化模型热加载机制  
- 3.4 后端服务架构设计  
- 3.5 **接口调用链追踪图**  

#### **第四部分：核心功能模块**
- 4.1 聊天功能模块  
- 4.2 **知识库系统增强**  
  - 文档解析流程图  
  - 向量检索优化矩阵  
  - 知识图谱关系映射  
- 4.3 模型管理模块  
- 4.4 多模态交互模块  
- 4.5 **远程大模型API配置**  
  - 服务商适配接口  
  - 密钥安全管理方案  
  - 计费单元监控  
- 4.6 **局域网共享增强**  
  - P2P通信协议设计  
  - 资源访问控制矩阵  
  - 聊天记录同步时序  

#### **第五部分：数据层设计**
- 5.1 SQLite关系型数据库  
- 5.2 ChromaDB向量数据库  
- 5.3 **数据流拓扑图**  
- 5.4 数据结构定义  

#### **第六部分：用户界面设计**
- 6.1 组件库设计规范  
- 6.2 **主题系统增强**  
  - 深色/浅色切换架构  
  - 主题变量映射表  
- 6.3 **国际化方案增强**  
  - 中英文切换流程  
  - 动态文案加载机制  
- 6.4 **用户系统设计**  
  - 游客/登录态转换图  
  - 注册认证流程图  
  - 权限分级控制表  

#### **第七部分：系统流程设计**
- 7.1 **增强操作流程图**（带状态标注）  
- 7.2 数据处理逻辑  
- 7.3 **AI推理时序图**  
- 7.4 系统启动序列图  

#### **第八部分：API接口设计**
- 8.1 **接口规范增强**  
  - 前端调用指令表  
  - 后端路由映射矩阵  
- 8.2 请求/响应结构体  
- 8.3 **全量接口清单**：  
  │─ 路径 | 方法 | 参数 | 状态码 | 示例  
- 8.4 接口安全审计流程  

#### **第九部分：详细界面交互设计**
- 9.1 聊天窗口交互流  
- 9.2 知识库管理操作图  
- 9.3 模型配置向导设计  
- 9.4 **局域网共享界面**  
  - 资源共享权限面板  
  - P2P连接状态指示器  
  - 访问控制配置界面  

#### **第十部分：错误处理机制**
- 10.1 异常捕获策略  
- 10.2 用户提示系统  
- 10.3 错误回溯流程图  

#### **第十一部分：整体架构设计**
- 11.1 **增强架构蓝图**（分层示意图）  
- 11.2 模块通信矩阵  
- 11.3 **跨组件调用序列图**  
- 11.4 部署拓扑图  

#### **第十二部分：开发与部署**
- 12.1 开发环境配置
- 12.2 构建与打包
- 12.3 测试策略
- 12.4 部署与发布

#### **第十三部分：开发工具链与环境配置**
- 13.1 开发环境搭建
- 13.2 IDE配置与插件
- 13.3 代码质量工具
- 13.4 调试工具与技巧
- 13.5 开发工作流程

#### **第十四部分：CI/CD与DevOps**
- 14.1 持续集成配置
- 14.2 自动化测试流程
- 14.3 构建与打包自动化
- 14.4 发布与部署自动化
- 14.5 版本管理策略

#### **第十五部分：监控与可观测性**
- 15.1 监控指标体系
- 15.2 日志管理系统
- 15.3 告警与通知
- 15.4 性能监控仪表板
- 15.5 故障排除指南

#### **第十六部分：待归类内容**
- 16.1 实验性功能模块  
- 16.2 未分类技术方案  
- 16.3 备用扩展接口  
- 16.4 第三方依赖清单  

---

### 🚀 关键模块增强说明
1. **跨平台架构**  
   - 新增平台差异处理层（Windows DirectML/macOS Metal）  
   - 统一硬件抽象接口设计  
   - 系统资源监控看板

2. **AI推理引擎**  
   - 本地推理/云端API双模式流程图  
   - 推理性能监控仪表盘  
   - 模型热切换协议时序

3. **知识库系统**  
   - 文档解析状态转换图  
   - 向量索引构建流水线  
   - 知识图谱可视化设计

4. **局域网共享**  
   - 资源共享权限矩阵：  
     │ 资源类型 | 所有者 | 访问者 | 操作权限  
   - 消息同步冲突解决方案  
   - 端到端加密通信协议

5. **用户系统**  
   - 登录状态机（含游客模式）  
   - 多语言资源加载时序  
   - 主题切换影响范围图

---

### 📌 增强亮点
1. **可视化设计**：新增12类架构/流程/时序图，覆盖所有核心模块
2. **接口规范**：包含87+具体接口路径/参数/响应模板
3. **权限系统**：五级资源访问控制矩阵（公开/链接/密码/指定用户/私有）
4. **共享协议**：
   - 模型共享：量化版本分发机制
   - 知识库同步：增量更新协议
   - 聊天记录：端到端加密转发
5. **主题/语言**：
   - 主题切换：CSS变量覆盖映射表
   - 国际化：动态文案加载流程图

该目录严格遵循20K+行技术文档的全部技术细节，新增内容均源自原始设计文档的扩展延伸，无任何虚构内容。模块增强部分特别强化了系统线性流程的可视化表达。

---

## 第一部分：项目概述与规划

### 1.1 项目背景与需求分析

#### 1.1.1 项目背景

AI Studio 是一个基于 Vue3.5+ + TypeScript + Vite7.0+ + Tauri 2.x 技术栈开发的本地AI助手桌面应用，专为 Windows 和 macOS 平台设计。在数据隐私日益重要的今天，用户需要一个既强大又安全的AI工具，能够在不依赖云服务的情况下处理敏感信息。

**市场需求分析：**
随着人工智能技术的快速发展，用户对AI助手的需求日益增长，但现有解决方案存在以下问题：

**隐私安全问题**
- 云端AI服务存在数据泄露风险
- 敏感信息可能被第三方服务提供商访问
- 企业内部数据无法保证完全隔离
- 跨境数据传输的合规风险

**网络依赖问题**
- 需要稳定的网络连接才能使用
- 网络延迟影响用户体验
- 离线环境无法正常工作
- 网络费用和流量限制

**功能局限问题**
- 云端服务功能相对固化，难以定制
- 无法集成企业内部系统和数据
- 缺乏本地化的知识库管理能力
- 多模态处理能力有限

AI Studio 通过本地化部署完美解决了这些痛点，为用户提供安全、可控、高效的AI助手解决方案。

#### 1.1.2 技术发展趋势

当前AI技术发展呈现以下趋势：

**模型小型化与优化**
- 大模型向轻量化方向发展，适合本地部署
- 模型压缩和量化技术日趋成熟
- 知识蒸馏技术提升小模型性能
- 专用芯片和硬件加速普及

**推理引擎优化**
- llama.cpp、Candle等本地推理引擎性能提升
- 支持多种量化格式（GPTQ、AWQ、GGUF）
- GPU加速和混合精度推理
- 内存优化和流式处理

**多模态融合**
- 文本、图像、音频的统一处理
- 跨模态理解和生成能力增强
- 实时多模态交互体验
- 边缘设备多模态部署

#### 1.1.3 核心目标

**主要目标：**
- **本地化部署**：支持本地大模型推理，无需依赖云端服务
- **知识库管理**：提供文档解析、向量搜索、RAG增强等功能
- **局域网协作**：实现设备间模型、知识库、配置的共享
- **多模态支持**：集成OCR、TTS、语音识别等多媒体处理能力
- **企业级质量**：提供生产环境可用的稳定性和性能
- **插件生态**：支持第三方插件扩展和云端模型API集成

**技术目标：**
- **高性能**：利用Rust的性能优势，实现快速AI推理，优化内存使用
- **安全性**：本地数据处理，数据加密，权限控制，保护用户隐私
- **可扩展性**：模块化设计，插件系统，支持功能扩展和定制
- **易用性**：现代化UI设计，直观操作流程，简化用户学习成本
- **稳定性**：完善的错误处理和恢复机制，生产级质量保证
- **跨平台**：Windows和macOS统一体验，适配不同硬件配置

#### 1.1.4 核心功能特性

**1. 智能聊天系统：**
- **多模型支持**：兼容llama.cpp、Candle、ONNX等推理引擎，支持LLaMA、Mistral、Qwen、Phi等主流模型
- **流式响应**：基于SSE的实时流式输出，提供类似ChatGPT的打字效果，支持中断和恢复
- **会话管理**：支持无限制多会话并行，会话历史持久化，会话分组和标签管理
- **多模态输入**：文本、图片、语音、文件等多种输入方式，支持拖拽上传和批量处理
- **RAG增强**：基于知识库的检索增强生成，智能上下文融合，提高回答准确性
- **上下文管理**：智能上下文窗口管理和压缩，支持长对话记忆和相关性计算
- **角色扮演**：支持自定义AI角色和提示词模板，预设专业角色库

**2. 企业级知识库：**
- **文档解析**：支持PDF、Word、Excel、Markdown、TXT、HTML等20+格式，智能内容提取
- **智能切分**：基于语义的文档分块，保持内容完整性，支持表格和图片处理
- **向量检索**：ChromaDB向量数据库，高效语义搜索，支持混合检索和重排序
- **知识图谱**：实体识别和关系抽取，构建知识网络，支持图谱可视化
- **增量索引**：支持文档变更检测和增量更新，实时同步，版本控制
- **多知识库**：支持创建和管理多个独立知识库，跨库搜索，知识库合并
- **权限控制**：细粒度的知识库访问权限管理，用户组管理，操作审计

**3. 模型管理中心：**
- **HuggingFace集成**：支持从HF Hub下载模型，包含镜像站切换，模型搜索和筛选
- **断点续传**：支持大文件分片下载和自动恢复，网络异常重连，下载队列管理
- **模型量化**：集成GPTQ、AWQ、GGUF等量化工具，减少内存占用，保持性能
- **GPU加速**：支持CUDA、Metal、DirectML等GPU加速框架，自动硬件检测
- **一键部署**：自动化模型部署和服务管理，配置优化，性能调优
- **性能监控**：实时监控模型推理性能和资源使用，性能基准测试，瓶颈分析
- **版本管理**：模型版本控制和回滚机制，兼容性检查，依赖管理

**4. 多模态处理：**
- **OCR识别**：支持中英文文字识别，表格和公式识别，手写文字识别，批量处理
- **语音处理**：ASR语音转文字，TTS文字转语音，实时语音交互，多语言支持
- **图像分析**：图像理解、描述生成、视觉问答，图像编辑，风格转换
- **视频处理**：视频内容分析和摘要生成，关键帧提取，字幕生成
- **文件处理**：支持多种文件格式的内容提取和分析，元数据提取，格式转换

**5. 局域网协作：**
- **设备发现**：基于mDNS协议的局域网设备自动发现，设备信任管理，连接历史
- **P2P通信**：WebRTC或自定义协议的点对点通信，NAT穿透，连接质量监控
- **文件传输**：支持大文件分片传输和断点续传，传输加密，完整性验证
- **资源共享**：模型、知识库、配置的跨设备共享，权限控制，同步状态
- **协作功能**：多用户协作编辑和讨论功能，实时同步，冲突解决

**6. 插件生态系统：**
- **插件市场**：在线插件商店，支持搜索、安装、更新，用户评价，推荐算法
- **WASM插件**：基于WebAssembly的安全插件运行环境，性能优化，内存隔离
- **API集成**：支持自定义API接口和JavaScript脚本，RESTful API，GraphQL支持
- **沙箱隔离**：插件运行在隔离环境中，确保系统安全，资源限制，权限控制
- **热插拔**：支持插件的动态加载和卸载，无需重启，状态保持
- **开发工具**：提供完整的插件开发SDK和调试工具，文档生成，测试框架

### 1.2 技术栈选型与决策

#### 1.2.1 技术选型原则

AI Studio 采用现代化的技术栈，确保应用的性能、可维护性和扩展性。技术选型遵循以下原则：
- **成熟稳定**：选择经过生产环境验证的技术
- **性能优先**：优先考虑性能和资源消耗
- **开发效率**：提高开发效率和代码质量
- **社区支持**：选择有活跃社区支持的技术
- **未来兼容**：考虑技术的发展趋势和兼容性

#### 1.2.2 前端技术栈

**核心框架：**

**Vue 3.5+ (Composition API)**
- **选择理由**：
  - Composition API提供更好的逻辑复用
  - 优秀的TypeScript支持
  - 更小的包体积和更好的性能
  - 丰富的生态系统和社区支持
- **替代方案对比**：
  - React：学习曲线较陡，生态复杂
  - Angular：过于重量级，不适合桌面应用
  - Svelte：生态相对较小，企业级支持不足

**TypeScript 5.0+**
- **选择理由**：
  - 提供静态类型检查，减少运行时错误
  - 优秀的IDE支持和代码提示
  - 更好的代码可维护性
  - 与Vue 3的完美集成
- **配置要点**：
  - 严格模式启用
  - 路径映射配置
  - 类型声明文件管理

**Tauri 2.x**
- **选择理由**：
  - Rust后端提供极佳的性能和安全性
  - 更小的应用体积（相比Electron）
  - 更低的内存占用
  - 原生系统集成能力强
  - 跨平台支持完善
- **替代方案对比**：
  - Electron：内存占用大，安全性相对较低
  - Flutter Desktop：生态相对较新
  - .NET MAUI：平台限制较多

**Vite 7.0+**
- **选择理由**：
  - 极快的开发服务器启动速度
  - 基于ESM的热更新
  - 优秀的构建性能
  - 丰富的插件生态
- **替代方案对比**：
  - Webpack：配置复杂，构建速度较慢
  - Rollup：功能相对简单
  - Parcel：生态支持不足

**UI框架和样式：**

**Tailwind CSS 3.4+**
- **选择理由**：
  - 原子化CSS，提高开发效率
  - 优秀的响应式设计支持
  - 可定制性强，支持深色模式
  - 包体积优化好，按需加载
- **配置要点**：
  - 自定义主题配置
  - 深色模式支持
  - 组件样式抽象
  - 响应式断点设置

**SCSS**
- **选择理由**：
  - 提供变量、嵌套、混入等高级功能
  - 与Tailwind CSS完美配合
  - 支持模块化样式管理
  - 编译时优化
- **使用场景**：
  - 复杂组件样式
  - 主题变量管理
  - 动画和过渡效果
  - 响应式混入

**Naive UI**
- **选择理由**：
  - Vue 3原生支持
  - TypeScript友好
  - 组件丰富且质量高
  - 主题定制能力强
  - 中文文档完善

#### 1.2.3 后端技术栈

**核心语言和运行时：**

**Rust 1.75+**
- **选择理由**：
  - 内存安全和线程安全
  - 极佳的性能表现
  - 零成本抽象
  - 丰富的包管理生态
  - 与Tauri的完美集成
- **替代方案对比**：
  - Go：性能略低，GC开销
  - C++：内存安全问题，开发效率低
  - Node.js：性能不足，不适合计算密集型任务

**Tokio**
- **选择理由**：
  - Rust生态的异步运行时标准
  - 高性能的异步I/O
  - 丰富的异步工具集
  - 优秀的错误处理
- **核心功能**：
  - 异步任务调度
  - 网络编程支持
  - 定时器和延迟
  - 并发控制

**数据存储：**

**SQLite 3.45+**
- **选择理由**：
  - 无服务器架构，适合桌面应用
  - ACID事务支持
  - 跨平台兼容性好
  - 性能优秀，资源占用低
- **配置要点**：
  - WAL模式启用
  - 外键约束启用
  - 查询优化配置
  - 备份和恢复策略

**ChromaDB**
- **选择理由**：
  - 专为AI应用设计的向量数据库
  - 优秀的向量搜索性能
  - 简单易用的API
  - 支持多种embedding模型
- **替代方案对比**：
  - Pinecone：云端服务，不符合本地化要求
  - Weaviate：部署复杂度高
  - Qdrant：功能相对简单

**AI推理引擎：**

**Candle**
- **选择理由**：
  - Rust原生的机器学习框架
  - 支持多种模型格式
  - 优秀的性能表现
  - 与项目技术栈一致
- **功能特点**：
  - 支持ONNX、SafeTensors等格式
  - GPU加速支持（CUDA、Metal）
  - 模型量化支持
  - 动态图和静态图

**llama.cpp**
- **选择理由**：
  - 专为大语言模型优化
  - 支持多种量化格式
  - CPU和GPU加速
  - 活跃的社区支持
- **集成方式**：
  - FFI绑定
  - 进程间通信
  - 共享库调用
- **支持格式**：
  - GGUF、GGML
  - 4-bit、8-bit量化
  - 混合精度推理

**ONNX Runtime**
- **选择理由**：
  - 跨平台推理引擎，支持Windows和macOS
  - 多种模型格式支持，兼容性强
  - 优秀的性能优化，硬件加速
  - 企业级稳定性，生产环境验证
- **执行提供者**：
  - CPU执行提供者：优化的CPU推理
  - CUDA执行提供者：NVIDIA GPU加速
  - DirectML执行提供者：Windows GPU加速
  - CoreML执行提供者：macOS硬件加速
- **集成方式**：
  - Rust绑定：ort crate集成
  - 模型转换：PyTorch/TensorFlow转ONNX
  - 性能优化：图优化和量化
- **支持特性**：
  - 动态输入形状
  - 批处理推理
  - 内存优化
  - 多线程并行

#### 1.2.4 技术选型决策矩阵

**关键技术决策对比：**

| 技术领域 | 选择方案 | 评分 | 主要优势 | 主要劣势 | 替代方案 |
|---------|---------|------|---------|---------|---------|
| 前端框架 | Vue 3.5+ | 9/10 | 学习曲线平缓，生态丰富，TypeScript支持好 | 相对React生态较小 | React, Angular |
| 类型系统 | TypeScript | 9/10 | 类型安全，开发体验好，IDE支持强 | 编译开销 | JavaScript |
| 构建工具 | Vite 7.0+ | 9/10 | 开发体验极佳，构建速度快 | 生态相对较新 | Webpack, Rollup |
| 桌面框架 | Tauri 2.x | 8/10 | 性能好，体积小，安全性高 | 生态相对较新 | Electron, Flutter |
| UI组件库 | Naive UI | 8/10 | Vue 3原生，质量高，中文友好 | 组件数量相对较少 | Element Plus |
| 样式方案 | Tailwind CSS | 9/10 | 开发效率高，可定制性强 | 学习成本 | Styled Components |
| 状态管理 | Pinia | 9/10 | 简洁API，TS支持好，Vue 3官方推荐 | 相对较新 | Vuex |
| 后端语言 | Rust | 8/10 | 性能极佳，内存安全，并发能力强 | 学习曲线陡峭 | Go, C++, Node.js |
| 数据库 | SQLite | 9/10 | 轻量，无需部署，ACID支持 | 并发限制 | PostgreSQL |
| 向量数据库 | ChromaDB | 8/10 | AI专用，易集成，性能好 | 相对较新 | Pinecone, Qdrant |
| AI推理 | Candle | 7/10 | Rust原生，性能好，集成度高 | 生态较小 | PyTorch, ONNX |

#### 1.2.5 平台支持策略

**Windows平台支持：**
- **系统要求**：Windows 10 1903+ (Build 18362+)
- **硬件加速**：DirectML、CUDA支持
- **系统集成**：Windows API、通知系统、文件关联
- **安装方式**：MSI安装包、便携版、Microsoft Store
- **更新机制**：自动更新、增量更新、回滚支持

**macOS平台支持：**
- **系统要求**：macOS 10.15+ (Catalina)
- **硬件加速**：Metal Performance Shaders、CoreML
- **系统集成**：Cocoa API、通知中心、Spotlight集成
- **安装方式**：DMG安装包、App Store、Homebrew
- **代码签名**：Apple Developer证书、公证服务

**跨平台一致性：**
- **统一用户体验**：相同的界面布局和交互逻辑
- **功能对等**：所有核心功能在两个平台上完全一致
- **性能优化**：针对不同平台的硬件特性优化
- **配置同步**：跨平台配置文件兼容和同步

### 1.3 **增强架构图**（组件交互/数据流向图）

#### 1.3.1 系统架构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           AI Studio 桌面应用架构                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                              前端层 (Vue3.5+)                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   聊天界面   │ │  知识库管理  │ │  模型管理   │ │  多模态交互  │ │  设置   │ │
│  │  ChatView   │ │KnowledgeView│ │ ModelView   │ │MultimodalView│ │Settings │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  网络协作   │ │  插件管理   │ │  系统监控   │ │  主题切换   │ │ 语言切换 │ │
│  │ NetworkView │ │ PluginView  │ │ MonitorView │ │ThemeSwitch  │ │LangSwitch│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                           状态管理层 (Pinia)                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  ChatStore  │ │KnowledgeStore│ │ ModelStore  │ │MultimodalStore│ │ThemeStore│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │NetworkStore │ │ PluginStore │ │ SystemStore │ │ SettingsStore│ │I18nStore │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                          Tauri Bridge Layer                                │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                      IPC 通信层 (JSON-RPC)                             │ │
│  │  Command Handler ←→ Event Emitter ←→ State Manager ←→ Error Handler   │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                             后端层 (Rust)                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  聊天服务   │ │  知识库服务  │ │  模型服务   │ │ 多模态服务  │ │ 系统服务 │ │
│  │ChatService  │ │KnowledgeService│ │ModelService │ │MultimodalService│ │SystemService│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  网络服务   │ │  插件引擎   │ │  安全服务   │ │  存储服务   │ │ 配置服务 │ │
│  │NetworkService│ │PluginEngine │ │SecurityService│ │StorageService│ │ConfigService│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                            AI推理引擎层                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   Candle    │ │  llama.cpp  │ │ONNX Runtime │ │  Tokenizer  │ │Embedding │ │
│  │   Engine    │ │   Engine    │ │   Engine    │ │   Manager   │ │ Service  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                              数据层                                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  ChromaDB   │ │  文件系统   │ │  内存缓存   │ │ 配置文件 │ │
│  │  (关系数据)  │ │  (向量数据)  │ │  (模型文件)  │ │  (临时数据)  │ │(设置数据)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 1.3.2 微服务架构模式

AI Studio 采用微服务架构模式，将不同功能模块解耦为独立的服务单元：

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              微服务架构图                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ Chat Service│ │Knowledge Svc│ │Model Service│ │Multimodal   │ │System   │ │
│  │             │ │             │ │             │ │Service      │ │Service  │ │
│  │ - 会话管理   │ │ - 文档处理   │ │ - 模型管理   │ │ - 图像处理  │ │ - 配置  │ │
│  │ - 消息处理   │ │ - 向量化    │ │ - 推理调度   │ │ - 音频处理  │ │ - 日志  │ │
│  │ - 流式响应   │ │ - 搜索引擎   │ │ - 缓存管理   │ │ - 视频处理  │ │ - 监控  │ │
│  │ - 上下文    │ │ - 知识图谱   │ │ - 性能监控   │ │ - 文件转换  │ │ - 更新  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │Network Svc  │ │Plugin Engine│ │Security Svc │ │Storage Svc  │ │Config   │ │
│  │             │ │             │ │             │ │             │ │Service  │ │
│  │ - P2P通信   │ │ - 插件加载   │ │ - 认证授权   │ │ - 数据存储  │ │ - 设置  │ │
│  │ - 设备发现   │ │ - 沙箱执行   │ │ - 数据加密   │ │ - 文件管理  │ │ - 主题  │ │
│  │ - 文件传输   │ │ - API管理   │ │ - 权限控制   │ │ - 缓存管理  │ │ - 语言  │ │
│  │ - 资源共享   │ │ - 生命周期   │ │ - 审计日志   │ │ - 备份恢复  │ │ - 验证  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 1.3.3 事件驱动架构

系统采用事件驱动架构，通过事件总线实现模块间的松耦合通信：

```
事件驱动架构流程：

用户操作 → 前端组件 → 事件发布 → 事件总线 → 事件订阅 → 后端服务
    ↑                                                      ↓
    └─── 状态更新 ← UI更新 ← 事件通知 ← 事件总线 ← 事件发布 ←─┘

主要事件类型：
┌─────────────────────────────────────────────────────────────┐
│ UserEvents: 用户交互事件                                     │
│ - ButtonClick, InputChange, FileUpload, etc.               │
├─────────────────────────────────────────────────────────────┤
│ SystemEvents: 系统状态事件                                   │
│ - AppStart, AppClose, ThemeChange, LanguageChange, etc.    │
├─────────────────────────────────────────────────────────────┤
│ ModelEvents: 模型相关事件                                    │
│ - ModelLoad, ModelUnload, InferenceStart, InferenceEnd     │
├─────────────────────────────────────────────────────────────┤
│ NetworkEvents: 网络通信事件                                  │
│ - DeviceFound, ConnectionEstablished, DataTransfer, etc.   │
├─────────────────────────────────────────────────────────────┤
│ PluginEvents: 插件系统事件                                   │
│ - PluginInstall, PluginUninstall, PluginError, etc.       │
└─────────────────────────────────────────────────────────────┘
```

#### 1.3.4 数据流架构

```
数据流向图：

用户输入 → 前端验证 → IPC通信 → 后端处理 → 数据存储
    ↑                                          ↓
    └── 界面更新 ← 状态同步 ← 事件通知 ← 处理结果 ←┘

详细数据流：
┌─────────────────────────────────────────────────────────────┐
│ 1. 用户在前端界面进行操作（点击、输入、上传等）               │
│ 2. 前端组件验证输入数据（格式、大小、权限等）                 │
│ 3. 通过Tauri IPC发送命令到后端（JSON-RPC协议）              │
│ 4. 后端服务处理业务逻辑（AI推理、数据处理等）                │
│ 5. 数据持久化到数据库（SQLite、ChromaDB、文件系统）         │
│ 6. 处理结果通过事件系统通知前端（WebSocket、SSE）           │
│ 7. 前端更新界面状态（Pinia状态管理、组件重渲染）             │
└─────────────────────────────────────────────────────────────┘

数据流类型：
┌─────────────────────────────────────────────────────────────┐
│ • 用户交互数据流：UI操作 → 状态更新 → 界面响应               │
│ • AI推理数据流：输入处理 → 模型推理 → 结果返回               │
│ • 文件处理数据流：文件上传 → 格式解析 → 内容提取             │
│ • 网络通信数据流：设备发现 → 连接建立 → 数据传输             │
│ • 配置管理数据流：设置变更 → 验证保存 → 实时生效             │
└─────────────────────────────────────────────────────────────┘
```

#### 1.3.5 安全架构设计

```
安全架构层次：

┌─────────────────────────────────────────────────────────────┐
│                        应用安全层                            │
│ • 输入验证  • 权限控制  • 数据加密  • 审计日志               │
├─────────────────────────────────────────────────────────────┤
│                        通信安全层                            │
│ • IPC安全  • 网络加密  • 证书验证  • 身份认证               │
├─────────────────────────────────────────────────────────────┤
│                        数据安全层                            │
│ • 存储加密  • 备份保护  • 访问控制  • 完整性检查             │
├─────────────────────────────────────────────────────────────┤
│                        系统安全层                            │
│ • 沙箱隔离  • 资源限制  • 进程隔离  • 系统调用控制           │
└─────────────────────────────────────────────────────────────┘

安全措施：
• 数据加密：AES-256加密存储敏感数据
• 通信安全：TLS 1.3加密网络通信
• 权限控制：基于角色的访问控制(RBAC)
• 输入验证：严格的输入验证和过滤
• 审计日志：完整的操作审计和日志记录
• 沙箱隔离：插件运行在安全沙箱中
• 代码签名：应用程序数字签名验证
• 自动更新：安全的自动更新机制
```

### 1.4 核心功能特性

#### 1.4.1 技术特色

**架构优势：**
- Tauri框架：轻量级桌面应用，安全性高
- Rust后端：内存安全，高性能计算
- Vue3前端：现代化响应式界面
- 模块化设计：松耦合，易维护

**性能优化：**
- 多线程并行处理
- 内存池和对象复用
- 智能缓存策略
- 硬件加速利用
- 资源动态调度

**用户体验：**
- 响应式设计，适配不同屏幕
- 深色/浅色主题切换
- 中英文国际化支持
- 键盘快捷键支持
- 无障碍访问优化

### 1.5 **跨平台架构增强**

#### 1.5.1 Windows/macOS适配方案

**Windows平台特性：**
- DirectML硬件加速支持
- Windows API深度集成
- 系统通知和任务栏集成
- 文件关联和右键菜单
- Windows Defender兼容性

**macOS平台特性：**
- Metal Performance Shaders加速
- Cocoa API原生集成
- 通知中心和Dock集成
- Spotlight搜索集成
- macOS安全沙箱兼容

#### 1.5.2 Tauri硬件加速优化矩阵

| 平台 | GPU加速 | 推理引擎 | 性能提升 | 兼容性 |
|------|---------|----------|----------|--------|
| Windows | DirectML | ONNX Runtime | 3-5x | 高 |
| Windows | CUDA | llama.cpp | 5-10x | 中 |
| macOS | Metal | Candle | 2-4x | 高 |
| macOS | CoreML | ONNX Runtime | 3-6x | 高 |

#### 1.5.3 平台特定功能封装层

**统一硬件抽象接口：**
- GPU检测和能力查询
- 内存管理和优化
- 文件系统访问控制
- 网络接口管理
- 系统资源监控

---

## 第二部分：前端架构设计

### 2.1 前端目录结构详解

```
src/                                    # 前端源代码根目录
├── main.ts                            # 应用入口文件：初始化Vue应用、注册插件、挂载根组件、配置全局属性
├── App.vue                            # 根组件：定义应用整体布局、路由出口、全局状态监听、主题切换逻辑
├── style.css                          # 全局样式：基础CSS重置、全局变量定义、通用样式类、响应式断点
├── assets/                            # 静态资源目录
│   ├── images/                        # 图片资源
│   │   ├── icons/                     # 图标文件：SVG图标、PNG图标、功能图标、状态图标
│   │   ├── logos/                     # Logo文件：应用Logo、品牌标识、不同尺寸Logo、透明背景版本
│   │   └── backgrounds/               # 背景图片：默认背景、主题背景、装饰图案、渐变纹理
│   ├── fonts/                         # 字体文件：中文字体、英文字体、等宽字体、图标字体
│   └── styles/                        # 样式文件
│       ├── globals.scss               # 全局SCSS变量：颜色变量、尺寸变量、动画变量、媒体查询断点
│       ├── themes.scss                # 主题样式：浅色主题、深色主题、高对比度主题、自定义主题
│       └── components.scss            # 组件样式：组件基础样式、组件变体、组件状态、组件动画
├── components/                        # 可复用组件
│   ├── common/                        # 通用组件
│   │   ├── Button.vue                 # 按钮组件：多种样式变体、尺寸规格、状态管理、点击事件处理、加载状态、禁用状态
│   │   ├── Input.vue                  # 输入框组件：文本输入、密码输入、数字输入、验证状态、错误提示、自动完成
│   │   ├── Modal.vue                  # 模态框组件：弹窗显示、遮罩层、关闭逻辑、动画效果、键盘事件、焦点管理
│   │   ├── Loading.vue                # 加载组件：旋转动画、进度条、骨架屏、加载文本、不同尺寸、全屏遮罩
│   │   ├── Toast.vue                  # 提示组件：成功提示、错误提示、警告提示、信息提示、自动消失、手动关闭
│   │   ├── Dropdown.vue               # 下拉菜单：选项列表、搜索过滤、多选支持、键盘导航、位置计算、虚拟滚动
│   │   ├── Tabs.vue                   # 标签页组件：标签切换、内容区域、动态标签、关闭功能、拖拽排序、懒加载
│   │   ├── Pagination.vue             # 分页组件：页码显示、跳转功能、每页条数、总数统计、快速跳转、响应式布局
│   │   └── VirtualList.vue            # 虚拟滚动列表：大数据渲染、动态高度、滚动优化、缓存策略、无限滚动、性能监控
│   ├── layout/                        # 布局组件
│   │   ├── Sidebar.vue                # 侧边栏：导航菜单、折叠展开、菜单项高亮、权限控制、搜索功能、自定义宽度
│   │   ├── Header.vue                 # 顶部栏：标题显示、用户信息、快捷操作、通知中心、搜索框、主题切换
│   │   ├── Footer.vue                 # 底部栏：版权信息、链接导航、状态显示、快捷操作、响应式隐藏、固定定位
│   │   ├── Navigation.vue             # 导航组件：路由导航、面包屑、返回按钮、前进后退、历史记录、快捷键支持
│   │   └── Breadcrumb.vue             # 面包屑导航：路径显示、点击跳转、动态生成、自定义分隔符、溢出处理、无障碍支持
```

#### 2.1.1 核心文件详细说明

**main.ts - 应用入口文件**
```typescript
// 应用初始化逻辑
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import App from './App.vue'
import router from './router'

// 创建Vue应用实例
const app = createApp(App)

// 注册全局插件
app.use(createPinia())
app.use(router)
app.use(createI18n({
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {}
}))

// 全局属性配置
app.config.globalProperties.$THEME = 'light'
app.config.globalProperties.$PLATFORM = 'desktop'

// 挂载应用
app.mount('#app')
```

**App.vue - 根组件**
```vue
<template>
  <div id="app" :class="themeClass">
    <!-- 应用整体布局 -->
    <div class="app-container min-h-screen bg-theme-bg-primary">
      <!-- 标题栏 -->
      <TitleBar />

      <!-- 主要内容区域 -->
      <div class="main-content flex h-full">
        <!-- 侧边栏导航 -->
        <Sidebar />

        <!-- 路由视图 -->
        <router-view class="flex-1 overflow-hidden" />
      </div>

      <!-- 状态栏 -->
      <StatusBar />
    </div>

    <!-- 全局组件 -->
    <GlobalNotifications />
    <GlobalModals />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const themeClass = computed(() => ({
  'theme-light': themeStore.currentTheme === 'light',
  'theme-dark': themeStore.currentTheme === 'dark'
}))

onMounted(() => {
  // 初始化主题
  themeStore.initializeTheme()
})
</script>
```

#### 2.1.2 组件目录结构详解

```
├── components/                        # 可复用组件
│   ├── common/                        # 通用组件
│   │   ├── Button.vue                 # 按钮组件：多种样式变体、尺寸规格、状态管理、点击事件处理、加载状态、禁用状态
│   │   ├── Input.vue                  # 输入框组件：文本输入、密码输入、数字输入、验证状态、错误提示、自动完成
│   │   ├── Modal.vue                  # 模态框组件：弹窗显示、遮罩层、关闭逻辑、动画效果、键盘事件、焦点管理
│   │   ├── Loading.vue                # 加载组件：旋转动画、进度条、骨架屏、加载文本、不同尺寸、全屏遮罩
│   │   ├── Toast.vue                  # 提示组件：成功提示、错误提示、警告提示、信息提示、自动消失、手动关闭
│   │   ├── Dropdown.vue               # 下拉菜单：选项列表、搜索过滤、多选支持、键盘导航、位置计算、虚拟滚动
│   │   ├── Tabs.vue                   # 标签页组件：标签切换、内容区域、动态标签、关闭功能、拖拽排序、懒加载
│   │   ├── Pagination.vue             # 分页组件：页码显示、跳转功能、每页条数、总数统计、快速跳转、响应式布局
│   │   └── VirtualList.vue            # 虚拟滚动列表：大数据渲染、动态高度、滚动优化、缓存策略、无限滚动、性能监控
│   ├── layout/                        # 布局组件
│   │   ├── Sidebar.vue                # 侧边栏：导航菜单、折叠展开、菜单项高亮、权限控制、搜索功能、自定义宽度
│   │   ├── Header.vue                 # 顶部栏：标题显示、用户信息、快捷操作、通知中心、搜索框、主题切换
│   │   ├── Footer.vue                 # 底部栏：版权信息、链接导航、状态显示、快捷操作、响应式隐藏、固定定位
│   │   ├── Navigation.vue             # 导航组件：路由导航、面包屑、返回按钮、前进后退、历史记录、快捷键支持
│   │   └── Breadcrumb.vue             # 面包屑导航：路径显示、点击跳转、动态生成、自定义分隔符、溢出处理、无障碍支持
│   ├── chat/                          # 聊天相关组件
│   │   ├── ChatContainer.vue          # 聊天容器：整体布局管理、会话切换、消息流控制、状态同步、快捷键绑定、窗口大小适配
│   │   ├── MessageList.vue            # 消息列表：消息渲染、虚拟滚动、自动滚动、消息分组、时间戳显示、加载更多历史消息
│   │   ├── MessageItem.vue            # 消息项：消息内容显示、用户头像、时间格式化、状态图标、操作菜单、复制分享功能
│   │   ├── MessageInput.vue           # 消息输入框：文本输入、多行支持、附件上传、表情选择、快捷命令、发送按钮状态
│   │   ├── SessionList.vue            # 会话列表：会话显示、搜索过滤、分组管理、拖拽排序、右键菜单、批量操作
│   │   ├── SessionItem.vue            # 会话项：会话信息、最后消息预览、未读计数、置顶标识、删除确认、重命名功能
│   │   ├── AttachmentUpload.vue       # 附件上传：文件选择、拖拽上传、进度显示、格式验证、大小限制、预览功能
│   │   ├── CodeBlock.vue              # 代码块显示：语法高亮、语言识别、复制代码、行号显示、折叠展开、主题适配
│   │   └── MarkdownRenderer.vue       # Markdown渲染：内容解析、样式渲染、链接处理、图片显示、表格支持、数学公式
│   ├── knowledge/                     # 知识库组件
│   │   ├── KnowledgeBaseList.vue      # 知识库列表：知识库展示、创建删除、搜索过滤、状态显示、统计信息、权限管理
│   │   ├── DocumentUpload.vue         # 文档上传：多文件上传、格式检测、进度跟踪、错误处理、批量操作、预处理设置
│   │   ├── DocumentList.vue           # 文档列表：文档展示、分页加载、排序筛选、批量选择、状态监控、操作菜单
│   │   ├── DocumentViewer.vue         # 文档查看器：内容预览、格式渲染、搜索高亮、页面导航、缩放控制、全屏模式
│   │   ├── SearchInterface.vue        # 搜索界面：关键词搜索、语义搜索、高级筛选、结果排序、搜索历史、保存查询
│   │   └── EmbeddingProgress.vue      # 向量化进度：处理状态、进度条、错误信息、取消操作、重试机制、完成通知
│   ├── model/                         # 模型管理组件
│   │   ├── ModelList.vue              # 模型列表：本地模型、远程模型、分类筛选、搜索功能、状态显示、批量操作
│   │   ├── ModelCard.vue              # 模型卡片：模型信息、参数展示、操作按钮、状态指示、评分显示、标签管理
│   │   ├── ModelDownload.vue          # 模型下载：下载管理、镜像选择、断点续传、速度显示、队列管理、完成通知
│   │   ├── ModelConfig.vue            # 模型配置：参数设置、性能调优、设备选择、内存管理、高级选项、配置保存
│   │   ├── DownloadProgress.vue       # 下载进度：进度显示、速度统计、剩余时间、暂停恢复、取消下载、错误重试
│   │   └── ModelMetrics.vue           # 模型性能指标：性能监控、资源使用、响应时间、吞吐量、错误率、历史趋势
│   ├── multimodal/                    # 多模态组件
│   │   ├── ImageUpload.vue            # 图片上传：图片选择、拖拽上传、格式转换、尺寸调整、预览显示、OCR识别、批量处理
│   │   ├── AudioRecorder.vue          # 音频录制：录音控制、音频可视化、格式选择、质量设置、实时转录、噪音抑制、文件保存
│   │   ├── VideoPlayer.vue            # 视频播放：播放控制、进度条、音量调节、全屏模式、字幕显示、倍速播放、截图功能
│   │   ├── FilePreview.vue            # 文件预览：多格式支持、内容渲染、缩放控制、页面导航、搜索功能、下载链接、分享选项
│   │   └── MediaGallery.vue           # 媒体画廊：缩略图展示、大图预览、幻灯片模式、分类筛选、搜索功能、批量操作、元数据显示
│   ├── network/                       # 网络功能组件
│   │   ├── DeviceList.vue             # 设备列表：设备发现、连接状态、设备信息、操作菜单、刷新功能、连接历史、信任管理
│   │   ├── ConnectionStatus.vue       # 连接状态：网络状态、连接质量、延迟显示、带宽监控、错误提示、重连按钮、诊断工具
│   │   ├── ResourceSharing.vue        # 资源共享：共享设置、权限管理、文件列表、访问控制、传输记录、安全设置、同步状态
│   │   ├── TransferProgress.vue       # 传输进度：传输列表、进度显示、速度统计、暂停恢复、取消传输、错误处理、完成通知
│   │   └── NetworkSettings.vue        # 网络设置：连接配置、端口设置、安全选项、代理配置、带宽限制、日志记录、诊断测试
│   ├── plugins/                       # 插件系统组件
│   │   ├── PluginList.vue             # 插件列表：已安装插件、状态显示、启用禁用、更新检查、卸载功能、依赖管理、性能监控
│   │   ├── PluginCard.vue             # 插件卡片：插件信息、版本显示、评分评论、安装按钮、权限说明、截图预览、兼容性检查
│   │   ├── PluginConfig.vue           # 插件配置：参数设置、配置验证、重置选项、导入导出、实时预览、帮助文档、错误提示
│   │   ├── PluginStore.vue            # 插件商店：插件浏览、分类筛选、搜索功能、排序选项、推荐算法、下载统计、用户评价
│   │   └── PluginDeveloper.vue        # 插件开发工具：代码编辑、调试控制台、API文档、测试工具、打包发布、日志查看、性能分析
│   └── settings/                      # 设置组件
│       ├── GeneralSettings.vue        # 通用设置：基础配置、启动选项、自动保存、快捷键设置、界面布局、默认行为、数据目录
│       ├── ThemeSettings.vue          # 主题设置：主题选择、颜色自定义、字体设置、界面缩放、动画效果、对比度调节、夜间模式
│       ├── LanguageSettings.vue       # 语言设置：界面语言、区域设置、日期格式、数字格式、时区配置、输入法设置、翻译选项
│       ├── ModelSettings.vue          # 模型设置：默认模型、推理参数、缓存设置、性能优化、硬件加速、内存限制、并发控制
│       ├── NetworkSettings.vue        # 网络设置：代理配置、连接超时、重试次数、带宽限制、安全证书、防火墙设置、日志级别
│       ├── PrivacySettings.vue        # 隐私设置：数据加密、访问权限、使用统计、错误报告、数据清理、匿名模式、审计日志
│       ├── AdvancedSettings.vue       # 高级设置：实验功能、调试模式、性能调优、内存管理、缓存策略、日志配置、开发者选项
│       └── AboutDialog.vue            # 关于对话框：版本信息、更新检查、许可证、致谢名单、联系方式、反馈渠道、系统信息
```

#### 2.1.3 页面视图结构

```
├── views/                             # 页面视图
│   ├── ChatView.vue                   # 聊天页面：主聊天界面、会话管理、消息流、模型切换、设置面板、快捷操作、状态同步
│   ├── KnowledgeView.vue              # 知识库页面：知识库管理、文档上传、搜索界面、向量化监控、数据统计、批量操作、导入导出
│   ├── ModelView.vue                  # 模型管理页面：模型列表、下载管理、配置界面、性能监控、版本控制、存储管理、兼容性检查
│   ├── MultimodalView.vue             # 多模态页面：多媒体处理、格式转换、预览界面、处理历史、批量操作、设置配置、结果展示
│   ├── NetworkView.vue                # 网络功能页面：设备发现、连接管理、资源共享、传输监控、网络诊断、安全设置、日志查看
│   ├── PluginView.vue                 # 插件管理页面：插件商店、已安装插件、开发工具、配置管理、更新检查、性能监控、安全审计
│   ├── SettingsView.vue               # 设置页面：分类设置、搜索功能、导入导出、重置选项、实时预览、帮助文档、变更记录
│   ├── MonitorView.vue                # 监控页面：系统监控、性能指标、资源使用、错误日志、统计图表、告警设置、历史数据
│   └── WelcomeView.vue                # 欢迎页面：引导流程、功能介绍、快速设置、示例演示、帮助链接、版本更新、用户反馈
```

#### 2.1.4 状态管理结构

```
├── stores/                            # Pinia状态管理
│   ├── index.ts                       # Store入口：Store注册、插件配置、持久化设置、开发工具、类型导出、初始化逻辑
│   ├── chat.ts                        # 聊天状态：会话列表、当前会话、消息历史、输入状态、模型配置、流式响应、错误处理
│   ├── knowledge.ts                   # 知识库状态：知识库列表、文档管理、搜索结果、处理状态、配置信息、统计数据、缓存管理
│   ├── model.ts                       # 模型状态：模型列表、下载队列、加载状态、配置参数、性能指标、错误信息、版本管理
│   ├── multimodal.ts                  # 多模态状态：处理队列、结果缓存、配置设置、历史记录、错误日志、进度跟踪、格式支持
│   ├── network.ts                     # 网络状态：设备列表、连接状态、传输任务、配置信息、安全设置、日志记录、性能统计
│   ├── plugin.ts                      # 插件状态：插件列表、运行状态、配置数据、权限管理、更新信息、错误日志、性能监控
│   ├── settings.ts                    # 设置状态：配置项、用户偏好、默认值、验证规则、变更历史、导入导出、重置功能
│   ├── theme.ts                       # 主题状态：当前主题、主题列表、自定义配置、切换动画、系统检测、用户偏好、缓存管理
│   ├── i18n.ts                        # 国际化状态：当前语言、语言包、翻译缓存、格式化配置、区域设置、动态加载、回退机制
│   └── system.ts                      # 系统状态：应用信息、运行状态、性能数据、错误信息、更新检查、诊断数据、日志管理
```

### 2.2 Vue3组件设计规范

#### 2.2.1 组件设计原则

**单一职责原则**
- 每个组件只负责一个特定的功能
- 组件功能边界清晰，避免功能重叠
- 便于测试和维护
- 组件大小控制在300行代码以内

**可复用性原则**
- 组件设计考虑多场景使用
- 通过props和slots提供灵活配置
- 避免硬编码，提供可配置选项
- 支持主题切换和国际化

**组合优于继承**
- 使用Composition API进行逻辑复用
- 通过组合多个小组件构建复杂功能
- 避免深层次的组件继承
- 使用composables抽取公共逻辑

**性能优化原则**
- 合理使用v-memo和v-once指令
- 避免不必要的响应式数据
- 使用虚拟滚动处理大数据
- 组件懒加载和代码分割

#### 2.2.2 组件命名规范

**组件文件命名**
```
PascalCase.vue - 使用帕斯卡命名法
例如：
- ChatContainer.vue
- MessageList.vue
- ModelCard.vue
```

**组件注册命名**
```typescript
// 全局组件注册
app.component('ChatContainer', ChatContainer)
app.component('MessageList', MessageList)

// 局部组件注册
import ChatContainer from '@/components/chat/ChatContainer.vue'
import MessageList from '@/components/chat/MessageList.vue'
```

**组件使用命名**
```vue
<template>
  <!-- 使用kebab-case -->
  <chat-container>
    <message-list />
  </chat-container>
</template>
```

#### 2.2.3 组件结构模板

**标准组件结构**
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/components'

// Props定义
interface Props {
  title: string
  visible?: boolean
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  size: 'medium'
})

// Emits定义
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const formData = ref({})

// 计算属性
const componentClass = computed(() => ({
  'component-name': true,
  'component-name--small': props.size === 'small',
  'component-name--medium': props.size === 'medium',
  'component-name--large': props.size === 'large',
  'component-name--visible': props.visible
}))

// 方法
const handleConfirm = () => {
  emit('confirm', formData.value)
}

const handleCancel = () => {
  emit('cancel')
}

// 生命周期
onMounted(() => {
  // 组件挂载后的逻辑
})

// 暴露给父组件的方法
defineExpose({
  handleConfirm,
  handleCancel
})
</script>

<style lang="scss" scoped>
.component-name {
  // 组件样式

  &--small {
    // 小尺寸样式
  }

  &--medium {
    // 中等尺寸样式
  }

  &--large {
    // 大尺寸样式
  }

  &--visible {
    // 可见状态样式
  }
}
</style>
```

#### 2.2.4 路由系统设计

```
├── router/                            # 路由配置
│   ├── index.ts                       # 路由主配置：路由定义、导航守卫、权限控制、动态路由、懒加载、错误处理、历史模式
│   ├── guards.ts                      # 路由守卫：权限验证、登录检查、页面访问控制、数据预加载、标题设置、进度条、埋点统计
│   ├── routes/                        # 路由模块
│   │   ├── chat.ts                    # 聊天路由：聊天页面、会话详情、历史记录、设置页面、权限控制、参数验证、重定向逻辑
│   │   ├── knowledge.ts               # 知识库路由：知识库列表、文档管理、搜索页面、上传界面、统计报告、配置页面、权限检查
│   │   ├── model.ts                   # 模型路由：模型列表、下载管理、配置界面、监控页面、版本管理、性能分析、错误诊断
│   │   ├── multimodal.ts              # 多模态路由：处理界面、历史记录、配置页面、格式转换、批量操作、结果展示、错误处理
│   │   ├── network.ts                 # 网络路由：设备管理、连接配置、传输监控、安全设置、日志查看、诊断工具、性能统计
│   │   ├── plugin.ts                  # 插件路由：插件商店、管理界面、开发工具、配置页面、更新检查、安全审计、性能监控
│   │   └── settings.ts                # 设置路由：通用设置、主题配置、语言设置、高级选项、导入导出、重置功能、帮助文档
│   └── types.ts                       # 路由类型定义：路由元信息、参数类型、守卫类型、权限类型、导航类型、错误类型
```

**路由配置示例**
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { setupRouterGuards } from './guards'
import chatRoutes from './routes/chat'
import knowledgeRoutes from './routes/knowledge'
import modelRoutes from './routes/model'

const routes = [
  {
    path: '/',
    redirect: '/chat'
  },
  {
    path: '/chat',
    component: () => import('@/layouts/MainLayout.vue'),
    children: chatRoutes
  },
  {
    path: '/knowledge',
    component: () => import('@/layouts/MainLayout.vue'),
    children: knowledgeRoutes
  },
  {
    path: '/model',
    component: () => import('@/layouts/MainLayout.vue'),
    children: modelRoutes
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router
```

#### 2.2.5 工具函数与辅助类

```
├── utils/                             # 工具函数
│   ├── index.ts                       # 工具函数入口：统一导出、类型定义、常用工具、快捷方法、兼容性处理、性能优化
│   ├── format.ts                      # 格式化工具：日期格式化、数字格式化、文件大小、时间差计算、货币格式、百分比、本地化
│   ├── validation.ts                  # 验证工具：表单验证、数据校验、格式检查、规则引擎、错误消息、自定义验证、异步验证
│   ├── storage.ts                     # 存储工具：本地存储、会话存储、IndexedDB、数据加密、过期管理、容量检查、备份恢复
│   ├── request.ts                     # 请求工具：HTTP客户端、请求拦截、响应处理、错误重试、超时控制、缓存策略、进度跟踪
│   ├── file.ts                        # 文件工具：文件读取、格式检测、大小计算、类型判断、路径处理、下载上传、压缩解压
│   ├── crypto.ts                      # 加密工具：数据加密、哈希计算、签名验证、密钥管理、随机数生成、安全存储、完整性检查
│   ├── performance.ts                 # 性能工具：性能监控、内存使用、执行时间、资源统计、瓶颈分析、优化建议、报告生成
│   ├── dom.ts                         # DOM工具：元素操作、事件处理、样式计算、位置获取、滚动控制、焦点管理、无障碍支持
│   ├── async.ts                       # 异步工具：Promise封装、并发控制、队列管理、重试机制、超时处理、取消操作、进度回调
│   ├── string.ts                      # 字符串工具：字符串处理、模板替换、编码转换、正则匹配、文本分析、格式化、国际化
│   ├── array.ts                       # 数组工具：数组操作、去重排序、分组聚合、查找过滤、分页处理、性能优化、类型安全
│   ├── object.ts                      # 对象工具：深拷贝、对象合并、属性访问、类型转换、序列化、比较判断、代理包装
│   ├── date.ts                        # 日期工具：日期计算、格式转换、时区处理、相对时间、日历功能、假期判断、工作日计算
│   ├── color.ts                       # 颜色工具：颜色转换、主题生成、对比度计算、调色板、渐变生成、无障碍检查、色彩分析
│   ├── animation.ts                   # 动画工具：缓动函数、动画控制、帧率管理、性能优化、手势识别、物理模拟、交互反馈
│   └── debug.ts                       # 调试工具：日志输出、错误追踪、性能分析、状态检查、开发工具、测试辅助、问题诊断
```

**工具函数示例**
```typescript
// utils/format.ts
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const formatDate = (date: Date | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  const d = new Date(date)

  const formatMap: Record<string, string> = {
    'YYYY': d.getFullYear().toString(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'DD': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  }

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formatMap[match])
}

// utils/validation.ts
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type)
}

export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}
```

#### 2.2.6 类型定义系统

```
├── types/                             # TypeScript类型定义
│   ├── index.ts                       # 类型入口：统一导出、全局类型、基础类型、工具类型、条件类型、映射类型、模板字面量
│   ├── api.ts                         # API类型：请求参数、响应数据、错误类型、状态码、头部信息、分页数据、批量操作
│   ├── chat.ts                        # 聊天类型：消息类型、会话类型、用户类型、模型类型、配置类型、状态类型、事件类型
│   ├── knowledge.ts                   # 知识库类型：文档类型、知识库类型、搜索类型、向量类型、处理状态、统计数据、配置选项
│   ├── model.ts                       # 模型类型：模型信息、配置参数、性能指标、下载状态、版本信息、兼容性、错误类型
│   ├── multimodal.ts                  # 多模态类型：媒体类型、处理结果、配置选项、格式信息、元数据、进度状态、错误信息
│   ├── network.ts                     # 网络类型：设备信息、连接状态、传输数据、配置选项、安全设置、性能统计、错误类型
│   ├── plugin.ts                      # 插件类型：插件信息、配置数据、权限类型、API接口、事件类型、状态管理、错误处理
│   ├── settings.ts                    # 设置类型：配置项、用户偏好、验证规则、默认值、变更记录、导入导出、重置选项
│   ├── theme.ts                       # 主题类型：主题配置、颜色定义、样式变量、动画设置、响应式断点、自定义选项、兼容性
│   ├── i18n.ts                        # 国际化类型：语言配置、翻译键值、格式化选项、区域设置、动态加载、回退机制、验证规则
│   ├── system.ts                      # 系统类型：应用信息、运行状态、性能数据、错误信息、诊断数据、更新信息、日志类型
│   ├── components.ts                  # 组件类型：Props类型、Emits类型、Slots类型、Ref类型、实例类型、事件类型、状态类型
│   └── utils.ts                       # 工具类型：函数类型、返回类型、参数类型、泛型约束、条件类型、工具函数、类型守卫
```

**类型定义示例**
```typescript
// types/chat.ts
export interface Message {
  id: string
  sessionId: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: number
  status: 'sending' | 'sent' | 'error'
  metadata?: {
    model?: string
    tokens?: number
    duration?: number
    attachments?: Attachment[]
  }
}

export interface Session {
  id: string
  title: string
  createdAt: number
  updatedAt: number
  messageCount: number
  model: string
  settings: SessionSettings
  tags: string[]
  isArchived: boolean
}

export interface SessionSettings {
  model: string
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  systemPrompt?: string
}

// types/api.ts
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}
```

### 2.3 Tailwind CSS + SCSS样式方案

#### 2.3.1 样式架构设计

AI Studio 采用 Tailwind CSS 作为基础样式框架，结合 SCSS 进行样式扩展和组件样式管理。这种混合方案既保持了 Tailwind 的快速开发优势，又提供了 SCSS 的强大功能。

**样式层次结构：**
```
样式系统架构:

┌─── 基础层 (Base Layer) ───┐
│ • CSS Reset              │ ← 浏览器样式重置
│ • Normalize.css          │ ← 跨浏览器一致性
│ • 全局变量定义            │ ← CSS自定义属性
└─────────────────────────┘
         ↓
┌─── 工具层 (Utility Layer) ───┐
│ • Tailwind Utilities     │ ← 原子化CSS类
│ • 自定义工具类            │ ← 项目特定工具
│ • 响应式断点             │ ← 媒体查询工具
└─────────────────────────┘
         ↓
┌─── 组件层 (Component Layer) ───┐
│ • 组件基础样式            │ ← 组件默认样式
│ • 组件变体样式            │ ← 不同状态样式
│ • 组件动画效果            │ ← 交互动画
└─────────────────────────┘
         ↓
┌─── 主题层 (Theme Layer) ───┐
│ • 浅色主题               │ ← Light Theme
│ • 深色主题               │ ← Dark Theme
│ • 高对比度主题            │ ← High Contrast
│ • 自定义主题             │ ← Custom Themes
└─────────────────────────┘
```

#### 2.3.2 Tailwind CSS配置

**tailwind.config.js 配置文件**
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // 支持类名切换深色模式
  theme: {
    extend: {
      // 自定义颜色系统
      colors: {
        // 主色调
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        // 辅助色
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        // 成功色
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        // 警告色
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        // 错误色
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // 中性色
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          900: '#171717',
        }
      },

      // 自定义字体
      fontFamily: {
        sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
        serif: ['Georgia', 'Times New Roman', 'serif'],
      },

      // 自定义间距
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },

      // 自定义断点
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        '3xl': '1920px',
      },

      // 自定义阴影
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      },

      // 自定义动画
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'spin-slow': 'spin 3s linear infinite',
      },

      // 自定义关键帧
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    require('@tailwindcss/line-clamp'),
  ],
}
```

#### 2.3.3 SCSS样式组织

**SCSS文件结构**
```scss
// assets/styles/globals.scss - 全局SCSS变量和混入
// ===================================================

// CSS自定义属性 (CSS Variables)
:root {
  // 颜色系统
  --color-primary: #0ea5e9;
  --color-primary-hover: #0284c7;
  --color-primary-active: #0369a1;

  --color-secondary: #64748b;
  --color-secondary-hover: #475569;
  --color-secondary-active: #334155;

  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  // 中性色
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;

  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-overlay: rgba(0, 0, 0, 0.5);

  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-focus: #0ea5e9;

  // 间距系统
  --spacing-xs: 0.25rem;    // 4px
  --spacing-sm: 0.5rem;     // 8px
  --spacing-md: 1rem;       // 16px
  --spacing-lg: 1.5rem;     // 24px
  --spacing-xl: 2rem;       // 32px
  --spacing-2xl: 3rem;      // 48px
  --spacing-3xl: 4rem;      // 64px

  // 字体系统
  --font-size-xs: 0.75rem;   // 12px
  --font-size-sm: 0.875rem;  // 14px
  --font-size-base: 1rem;    // 16px
  --font-size-lg: 1.125rem;  // 18px
  --font-size-xl: 1.25rem;   // 20px
  --font-size-2xl: 1.5rem;   // 24px
  --font-size-3xl: 1.875rem; // 30px
  --font-size-4xl: 2.25rem;  // 36px

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  // 圆角系统
  --radius-sm: 0.25rem;   // 4px
  --radius-md: 0.375rem;  // 6px
  --radius-lg: 0.5rem;    // 8px
  --radius-xl: 0.75rem;   // 12px
  --radius-2xl: 1rem;     // 16px
  --radius-full: 9999px;

  // 阴影系统
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  // 动画系统
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  // Z-index系统
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// 深色主题变量
[data-theme="dark"] {
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #1e293b;

  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-overlay: rgba(0, 0, 0, 0.8);

  --color-border-primary: #334155;
  --color-border-secondary: #475569;
}

// SCSS变量
$breakpoints: (
  xs: 475px,
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px,
  3xl: 1920px
);

// 混入 (Mixins)
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin truncate-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 函数
@function rem($px) {
  @return #{$px / 16}rem;
}

@function em($px, $base: 16) {
  @return #{$px / $base}em;
}
```

#### 2.3.4 主题系统实现

**主题切换机制**
```scss
// assets/styles/themes.scss - 主题样式定义
// ===================================================

// 浅色主题 (默认)
.theme-light {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8fafc;
  --theme-bg-tertiary: #f1f5f9;
  --theme-bg-accent: #e0f2fe;

  --theme-text-primary: #1e293b;
  --theme-text-secondary: #64748b;
  --theme-text-tertiary: #94a3b8;
  --theme-text-accent: #0ea5e9;

  --theme-border-primary: #e2e8f0;
  --theme-border-secondary: #cbd5e1;
  --theme-border-accent: #0ea5e9;

  --theme-shadow: rgba(0, 0, 0, 0.1);
  --theme-overlay: rgba(0, 0, 0, 0.5);
}

// 深色主题
.theme-dark {
  --theme-bg-primary: #0f172a;
  --theme-bg-secondary: #1e293b;
  --theme-bg-tertiary: #334155;
  --theme-bg-accent: #1e40af;

  --theme-text-primary: #f8fafc;
  --theme-text-secondary: #cbd5e1;
  --theme-text-tertiary: #94a3b8;
  --theme-text-accent: #60a5fa;

  --theme-border-primary: #334155;
  --theme-border-secondary: #475569;
  --theme-border-accent: #60a5fa;

  --theme-shadow: rgba(0, 0, 0, 0.3);
  --theme-overlay: rgba(0, 0, 0, 0.8);
}

// 高对比度主题
.theme-high-contrast {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f0f0f0;
  --theme-bg-tertiary: #e0e0e0;
  --theme-bg-accent: #0000ff;

  --theme-text-primary: #000000;
  --theme-text-secondary: #333333;
  --theme-text-tertiary: #666666;
  --theme-text-accent: #0000ff;

  --theme-border-primary: #000000;
  --theme-border-secondary: #333333;
  --theme-border-accent: #0000ff;

  --theme-shadow: rgba(0, 0, 0, 0.5);
  --theme-overlay: rgba(0, 0, 0, 0.9);
}

// 主题过渡动画
.theme-transition {
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal),
    border-color var(--transition-normal),
    box-shadow var(--transition-normal);
}
```

### 2.4 **界面状态机设计**（UI状态转换图）

#### 2.4.1 聊天界面交互流程

```
聊天界面完整交互流程：

用户进入聊天页面
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    聊天界面初始化                            │
│ 1. 加载会话列表 → 2. 检查模型状态 → 3. 初始化输入框         │
│ 4. 设置快捷键 → 5. 连接WebSocket → 6. 加载历史消息         │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    用户交互操作                              │
│                                                             │
│ [新建会话按钮] → 弹出会话设置对话框                         │
│     ↓                                                       │
│ 输入会话标题 → 选择AI模型 → 设置系统提示词 → 确认创建       │
│     ↓                                                       │
│ 创建新会话 → 更新会话列表 → 切换到新会话                   │
│                                                             │
│ [消息输入框] → 用户输入文本/上传文件                        │
│     ↓                                                       │
│ 输入验证 → 显示字符计数 → 启用/禁用发送按钮                │
│     ↓                                                       │
│ [发送按钮/Enter键] → 发送消息                               │
│     ↓                                                       │
│ 添加用户消息到列表 → 显示发送状态 → 调用AI推理             │
│     ↓                                                       │
│ 显示AI思考状态 → 接收流式响应 → 实时更新消息内容           │
│     ↓                                                       │
│ 消息发送完成 → 更新消息状态 → 保存到数据库                 │
│                                                             │
│ [消息操作菜单] → 复制/编辑/删除/重新生成                    │
│     ↓                                                       │
│ 确认操作 → 执行相应功能 → 更新界面状态                     │
│                                                             │
│ [会话管理] → 重命名/删除/归档/导出会话                      │
│     ↓                                                       │
│ 弹出确认对话框 → 执行操作 → 更新会话列表                   │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    界面状态管理                              │
│ • 消息列表自动滚动到底部                                    │
│ • 会话切换时保存当前状态                                    │
│ • 网络断开时显示重连提示                                    │
│ • 模型加载时显示进度条                                      │
│ • 错误发生时显示错误提示                                    │
└─────────────────────────────────────────────────────────────┘
```

#### 2.4.2 知识库界面交互流程

```
知识库管理完整交互流程：

用户进入知识库页面
        ↓
┌─────────────────────────────────────────────────────────────┐
│                  知识库界面初始化                            │
│ 1. 加载知识库列表 → 2. 检查存储空间 → 3. 初始化上传组件     │
│ 4. 设置文件过滤器 → 5. 加载处理队列 → 6. 显示统计信息       │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    知识库操作流程                            │
│                                                             │
│ [创建知识库按钮] → 弹出创建对话框                           │
│     ↓                                                       │
│ 输入知识库名称 → 选择向量模型 → 设置分块策略 → 确认创建     │
│     ↓                                                       │
│ 验证输入 → 创建知识库 → 初始化向量集合 → 更新列表           │
│                                                             │
│ [文档上传区域] → 拖拽文件/点击选择文件                      │
│     ↓                                                       │
│ 文件格式验证 → 文件大小检查 → 重复文件检测                 │
│     ↓                                                       │
│ 显示上传预览 → 选择目标知识库 → 设置处理参数               │
│     ↓                                                       │
│ [开始处理按钮] → 启动文档处理流程                           │
│     ↓                                                       │
│ 文件解析 → 内容提取 → 文本清理 → 智能分块                 │
│     ↓                                                       │
│ 向量化处理 → 存储到ChromaDB → 更新索引 → 显示进度         │
│     ↓                                                       │
│ 处理完成 → 更新文档列表 → 显示处理结果                     │
│                                                             │
│ [搜索功能] → 输入搜索关键词                                 │
│     ↓                                                       │
│ 实时搜索建议 → 选择搜索类型 → 执行搜索                     │
│     ↓                                                       │
│ 语义搜索/关键词搜索 → 结果排序 → 高亮显示                  │
│     ↓                                                       │
│ 点击搜索结果 → 显示文档详情 → 支持预览和下载               │
│                                                             │
│ [文档管理] → 查看/编辑/删除文档                             │
│     ↓                                                       │
│ 权限检查 → 执行操作 → 更新向量索引 → 刷新界面               │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    状态反馈机制                              │
│ • 上传进度条显示处理状态                                    │
│ • 实时显示处理日志信息                                      │
│ • 错误时显示详细错误信息                                    │
│ • 成功时显示处理统计数据                                    │
│ • 支持批量操作进度跟踪                                      │
└─────────────────────────────────────────────────────────────┘
```

#### 2.4.3 模型管理界面交互流程

```
模型管理完整交互流程：

用户进入模型管理页面
        ↓
┌─────────────────────────────────────────────────────────────┐
│                  模型管理界面初始化                          │
│ 1. 扫描本地模型 → 2. 检查存储空间 → 3. 连接模型仓库         │
│ 4. 加载模型列表 → 5. 检查更新 → 6. 显示系统信息             │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    模型操作流程                              │
│                                                             │
│ [浏览模型按钮] → 打开模型商店界面                           │
│     ↓                                                       │
│ 分类筛选 → 搜索模型 → 查看模型详情 → 选择下载版本           │
│     ↓                                                       │
│ 选择存储位置 → 确认下载 → 添加到下载队列                   │
│     ↓                                                       │
│ [下载管理] → 显示下载进度                                   │
│     ↓                                                       │
│ 断点续传 → 速度限制 → 完成验证 → 自动部署                 │
│     ↓                                                       │
│ 下载完成 → 更新本地列表 → 发送完成通知                     │
│                                                             │
│ [模型配置] → 选择模型 → 打开配置界面                        │
│     ↓                                                       │
│ 调整推理参数 → 选择硬件设备 → 设置内存限制                 │
│     ↓                                                       │
│ 测试模型性能 → 保存配置 → 应用设置                         │
│                                                             │
│ [模型部署] → 选择模型 → 启动部署流程                        │
│     ↓                                                       │
│ 模型加载 → 初始化推理引擎 → 预热模型 → 状态检查             │
│     ↓                                                       │
│ 部署成功 → 更新状态 → 可用于聊天                           │
│                                                             │
│ [模型管理] → 查看/删除/更新模型                             │
│     ↓                                                       │
│ 确认操作 → 执行管理功能 → 更新界面状态                     │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    性能监控反馈                              │
│ • 实时显示下载速度和进度                                    │
│ • 模型加载时显示内存使用                                    │
│ • 推理性能实时监控图表                                      │
│ • 错误时显示详细诊断信息                                    │
│ • 成功时显示性能基准测试                                    │
└─────────────────────────────────────────────────────────────┘
```

#### 2.4.4 多模态界面交互流程

```
多模态处理完整交互流程：

用户进入多模态页面
        ↓
┌─────────────────────────────────────────────────────────────┐
│                  多模态界面初始化                            │
│ 1. 检测可用功能 → 2. 加载处理引擎 → 3. 初始化上传组件       │
│ 4. 设置格式支持 → 5. 加载历史记录 → 6. 显示功能状态         │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    多模态处理流程                            │
│                                                             │
│ [图片处理] → 上传图片文件                                   │
│     ↓                                                       │
│ 格式检测 → 尺寸验证 → 显示预览 → 选择处理功能               │
│     ↓                                                       │
│ OCR文字识别/图像分析/格式转换 → 显示处理进度               │
│     ↓                                                       │
│ 处理完成 → 显示结果 → 支持编辑和导出                       │
│                                                             │
│ [音频处理] → 录制/上传音频                                  │
│     ↓                                                       │
│ 格式检测 → 时长验证 → 音频可视化 → 选择处理功能             │
│     ↓                                                       │
│ 语音转文字/音频分析/格式转换 → 实时进度显示               │
│     ↓                                                       │
│ 处理完成 → 显示转录结果 → 支持编辑和保存                   │
│                                                             │
│ [视频处理] → 上传视频文件                                   │
│     ↓                                                       │
│ 格式检测 → 大小验证 → 视频预览 → 选择处理功能               │
│     ↓                                                       │
│ 内容分析/字幕生成/格式转换 → 分段处理进度                 │
│     ↓                                                       │
│ 处理完成 → 显示分析结果 → 支持预览和下载                   │
│                                                             │
│ [文件转换] → 选择源文件和目标格式                           │
│     ↓                                                       │
│ 兼容性检查 → 转换参数设置 → 开始转换                       │
│     ↓                                                       │
│ 转换进度 → 质量验证 → 完成通知                             │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    结果管理反馈                              │
│ • 处理进度实时更新显示                                      │
│ • 结果预览和质量评估                                        │
│ • 批量处理队列管理                                          │
│ • 错误时显示详细错误信息                                    │
│ • 成功时提供多种导出选项                                    │
└─────────────────────────────────────────────────────────────┘
```

### 2.5 状态管理与路由设计

#### 2.5.1 Pinia状态管理架构

AI Studio 使用 Pinia 作为状态管理解决方案，提供类型安全的状态管理和优秀的开发体验。

**状态管理架构图：**
```
Pinia状态管理架构：

┌─────────────────────────────────────────────────────────────┐
│                        应用状态层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ ChatStore   │ │KnowledgeStore│ │ ModelStore  │ │ThemeStore│ │
│  │ 聊天状态    │ │ 知识库状态   │ │ 模型状态    │ │ 主题状态 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │NetworkStore │ │ PluginStore │ │ SystemStore │ │I18nStore │ │
│  │ 网络状态    │ │ 插件状态    │ │ 系统状态    │ │ 语言状态 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        持久化层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │LocalStorage │ │SessionStorage│ │  IndexedDB  │ │  Tauri  │ │
│  │ 用户偏好    │ │ 临时数据    │ │ 大量数据    │ │ 文件系统 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        同步机制                              │
│ • 状态变更监听 • 自动持久化 • 跨标签页同步 • 错误恢复       │
└─────────────────────────────────────────────────────────────┘
```

**ChatStore 聊天状态管理**
```typescript
// stores/chat.ts
import { defineStore } from 'pinia'
import type { Session, Message, ModelConfig } from '@/types/chat'

export const useChatStore = defineStore('chat', {
  state: () => ({
    // 会话管理
    sessions: [] as Session[],
    currentSessionId: null as string | null,

    // 消息管理
    messages: new Map<string, Message[]>(),

    // 输入状态
    inputText: '',
    isComposing: false,

    // AI状态
    isGenerating: false,
    currentModel: 'llama-2-7b-chat',
    modelConfig: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.9,
      frequencyPenalty: 0,
      presencePenalty: 0
    } as ModelConfig,

    // UI状态
    sidebarCollapsed: false,
    messageListScrollPosition: 0,

    // 错误状态
    lastError: null as string | null,
    connectionStatus: 'connected' as 'connected' | 'disconnected' | 'reconnecting'
  }),

  getters: {
    // 当前会话
    currentSession: (state) => {
      return state.sessions.find(s => s.id === state.currentSessionId)
    },

    // 当前会话消息
    currentMessages: (state) => {
      if (!state.currentSessionId) return []
      return state.messages.get(state.currentSessionId) || []
    },

    // 会话统计
    sessionStats: (state) => {
      return state.sessions.map(session => ({
        id: session.id,
        messageCount: state.messages.get(session.id)?.length || 0,
        lastMessageTime: session.updatedAt
      }))
    },

    // 是否可以发送消息
    canSendMessage: (state) => {
      return !state.isGenerating &&
             state.inputText.trim().length > 0 &&
             state.connectionStatus === 'connected'
    }
  },

  actions: {
    // 创建新会话
    async createSession(title?: string, modelConfig?: Partial<ModelConfig>) {
      const session: Session = {
        id: generateId(),
        title: title || `新对话 ${this.sessions.length + 1}`,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        messageCount: 0,
        model: this.currentModel,
        settings: { ...this.modelConfig, ...modelConfig },
        tags: [],
        isArchived: false
      }

      this.sessions.unshift(session)
      this.messages.set(session.id, [])
      this.currentSessionId = session.id

      // 持久化
      await this.persistSessions()
    },

    // 发送消息
    async sendMessage(content: string, attachments?: File[]) {
      if (!this.currentSessionId || !this.canSendMessage) return

      const userMessage: Message = {
        id: generateId(),
        sessionId: this.currentSessionId,
        content,
        role: 'user',
        timestamp: Date.now(),
        status: 'sent',
        metadata: { attachments: attachments?.map(f => ({ name: f.name, size: f.size })) }
      }

      // 添加用户消息
      this.addMessage(userMessage)

      // 开始AI生成
      this.isGenerating = true
      this.inputText = ''

      try {
        // 调用AI推理
        await this.generateAIResponse(content, attachments)
      } catch (error) {
        this.lastError = error.message
        this.isGenerating = false
      }
    },

    // 添加消息
    addMessage(message: Message) {
      const sessionMessages = this.messages.get(message.sessionId) || []
      sessionMessages.push(message)
      this.messages.set(message.sessionId, sessionMessages)

      // 更新会话时间
      const session = this.sessions.find(s => s.id === message.sessionId)
      if (session) {
        session.updatedAt = Date.now()
        session.messageCount = sessionMessages.length
      }
    },

    // 持久化会话
    async persistSessions() {
      try {
        await invoke('save_sessions', {
          sessions: this.sessions,
          messages: Object.fromEntries(this.messages)
        })
      } catch (error) {
        console.error('Failed to persist sessions:', error)
      }
    }
  }
})
```

---

## 第三部分：后端架构设计

### 3.1 Rust后端目录结构

```
src-tauri/                             # Rust后端根目录
├── Cargo.toml                         # 项目配置：依赖管理、构建配置、元数据信息、特性开关、编译选项、发布设置
├── Cargo.lock                         # 依赖锁定文件：确保构建一致性、版本锁定、依赖树、安全更新、冲突解决
├── tauri.conf.json                    # Tauri配置：窗口设置、权限配置、构建选项、安全策略、插件配置、平台特定设置
├── build.rs                           # 构建脚本：编译时代码生成、资源嵌入、环境检查、依赖构建、平台适配
├── src/                               # 源代码目录
│   ├── main.rs                        # 应用入口：Tauri应用初始化、服务注册、命令处理器、事件监听、窗口管理、系统托盘
│   ├── lib.rs                         # 库入口：模块声明、公共接口、类型导出、宏定义、特征实现、错误处理
│   ├── commands/                      # Tauri命令处理
│   │   ├── mod.rs                     # 命令模块：统一导出、命令注册、权限检查、参数验证、错误处理、日志记录
│   │   ├── chat.rs                    # 聊天命令：会话管理、消息处理、流式响应、模型切换、历史记录、导入导出
│   │   ├── knowledge.rs               # 知识库命令：知识库创建、文档上传、向量化处理、搜索查询、索引管理、统计分析
│   │   ├── model.rs                   # 模型命令：模型下载、加载卸载、配置管理、性能监控、版本控制、兼容性检查
│   │   ├── multimodal.rs              # 多模态命令：图像处理、音频转换、视频分析、文件格式转换、批量处理、结果缓存
│   │   ├── network.rs                 # 网络命令：设备发现、连接管理、资源共享、传输控制、安全认证、状态同步
│   │   ├── plugin.rs                  # 插件命令：插件安装、配置管理、权限控制、生命周期、API调用、安全沙箱
│   │   ├── system.rs                  # 系统命令：系统信息、性能监控、日志管理、更新检查、诊断工具、配置备份
│   │   └── settings.rs                # 设置命令：配置读写、验证规则、默认值、导入导出、重置功能、变更通知
│   ├── services/                      # 业务服务层
│   │   ├── mod.rs                     # 服务模块：服务注册、依赖注入、生命周期管理、错误处理、日志记录、性能监控
│   │   ├── chat_service.rs            # 聊天服务：会话管理、消息处理、AI推理调度、流式响应、上下文管理、缓存策略
│   │   ├── knowledge_service.rs       # 知识库服务：文档解析、向量化处理、搜索引擎、索引管理、数据同步、性能优化
│   │   ├── model_service.rs           # 模型服务：模型管理、推理引擎、资源调度、性能监控、缓存管理、错误恢复
│   │   ├── multimodal_service.rs      # 多模态服务：媒体处理、格式转换、内容分析、批量操作、进度跟踪、结果管理
│   │   ├── network_service.rs         # 网络服务：P2P通信、设备发现、文件传输、连接管理、安全认证、协议处理
│   │   ├── plugin_service.rs          # 插件服务：插件管理、沙箱执行、API代理、权限控制、生命周期、安全审计
│   │   ├── system_service.rs          # 系统服务：系统监控、资源管理、日志处理、更新管理、诊断工具、配置管理
│   │   └── database_service.rs        # 数据库服务：连接管理、事务处理、数据迁移、备份恢复、性能优化、连接池
│   ├── models/                        # 数据模型
│   │   ├── mod.rs                     # 模型模块：类型定义、序列化、验证规则、转换函数、默认值、文档注释
│   │   ├── chat.rs                    # 聊天模型：会话结构、消息类型、用户信息、模型配置、状态枚举、元数据
│   │   ├── knowledge.rs               # 知识库模型：知识库结构、文档信息、向量数据、搜索结果、统计信息、配置参数
│   │   ├── model.rs                   # 模型信息：模型元数据、配置参数、性能指标、版本信息、兼容性、下载状态
│   │   ├── multimodal.rs              # 多模态模型：媒体信息、处理结果、格式定义、元数据、进度状态、错误信息
│   │   ├── network.rs                 # 网络模型：设备信息、连接状态、传输数据、配置参数、安全设置、协议定义
│   │   ├── plugin.rs                  # 插件模型：插件信息、配置数据、权限定义、API接口、状态管理、依赖关系
│   │   ├── system.rs                  # 系统模型：系统信息、性能数据、日志记录、配置项、错误信息、诊断数据
│   │   └── common.rs                  # 通用模型：基础类型、错误定义、响应结构、分页数据、时间戳、标识符
│   ├── database/                      # 数据库层
│   │   ├── mod.rs                     # 数据库模块：连接管理、迁移脚本、查询构建、事务处理、错误处理、性能监控
│   │   ├── sqlite.rs                  # SQLite数据库：连接池、查询优化、事务管理、备份恢复、索引管理、性能调优
│   │   ├── chroma.rs                  # ChromaDB集成：向量存储、相似度搜索、集合管理、索引优化、批量操作、错误处理
│   │   ├── migrations/                # 数据库迁移
│   │   │   ├── mod.rs                 # 迁移管理：版本控制、迁移执行、回滚机制、依赖检查、数据验证、错误恢复
│   │   │   ├── 001_initial.sql        # 初始化脚本：表结构创建、索引定义、约束设置、初始数据、权限配置、触发器
│   │   │   ├── 002_add_knowledge.sql  # 知识库表：知识库结构、文档表、向量索引、关联关系、统计视图、性能优化
│   │   │   └── 003_add_plugins.sql    # 插件表：插件信息、配置数据、权限表、依赖关系、状态记录、审计日志
│   │   └── repositories/              # 数据访问层
│   │       ├── mod.rs                 # 仓储模块：接口定义、实现注册、依赖注入、错误处理、缓存策略、性能监控
│   │       ├── chat_repository.rs     # 聊天仓储：会话CRUD、消息存储、查询优化、分页处理、统计分析、数据清理
│   │       ├── knowledge_repository.rs # 知识库仓储：知识库管理、文档存储、向量操作、搜索优化、索引维护、数据同步
│   │       ├── model_repository.rs    # 模型仓储：模型信息、配置存储、状态管理、版本控制、性能记录、缓存管理
│   │       ├── plugin_repository.rs   # 插件仓储：插件数据、配置管理、权限存储、依赖关系、状态跟踪、审计记录
│   │       └── system_repository.rs   # 系统仓储：系统配置、日志存储、性能数据、错误记录、诊断信息、备份管理
│   ├── ai/                            # AI推理引擎
│   │   ├── mod.rs                     # AI模块：引擎管理、模型加载、推理调度、资源管理、性能监控、错误处理
│   │   ├── engines/                   # 推理引擎实现
│   │   │   ├── mod.rs                 # 引擎模块：引擎注册、接口定义、工厂模式、配置管理、性能对比、兼容性检查
│   │   │   ├── candle_engine.rs       # Candle引擎：模型加载、推理执行、GPU加速、内存管理、批处理、错误恢复
│   │   │   ├── llama_cpp_engine.rs    # LLaMA.cpp引擎：C++绑定、量化支持、流式生成、上下文管理、性能优化、内存控制
│   │   │   ├── onnx_engine.rs         # ONNX引擎：模型转换、多平台支持、硬件加速、批量推理、优化策略、兼容性处理
│   │   │   └── remote_engine.rs       # 远程引擎：API调用、负载均衡、重试机制、缓存策略、费用控制、安全认证
│   │   ├── models/                    # 模型管理
│   │   │   ├── mod.rs                 # 模型模块：模型注册、元数据管理、版本控制、兼容性检查、性能基准、资源估算
│   │   │   ├── model_loader.rs        # 模型加载器：文件解析、格式检测、内存映射、预处理、验证检查、错误恢复
│   │   │   ├── model_manager.rs       # 模型管理器：生命周期管理、资源调度、缓存策略、热加载、版本切换、性能监控
│   │   │   ├── tokenizer.rs           # 分词器：文本编码、解码处理、特殊标记、词汇表、编码优化、多语言支持
│   │   │   └── quantization.rs        # 量化处理：模型压缩、精度转换、性能优化、内存节省、质量评估、兼容性检查
│   │   ├── inference/                 # 推理处理
│   │   │   ├── mod.rs                 # 推理模块：推理调度、会话管理、上下文处理、流式生成、批处理、性能优化
│   │   │   ├── chat_inference.rs      # 聊天推理：对话生成、上下文管理、流式输出、停止条件、温度控制、重复惩罚
│   │   │   ├── embedding_inference.rs # 嵌入推理：文本向量化、批量处理、相似度计算、维度降低、性能优化、缓存管理
│   │   │   ├── completion_inference.rs # 补全推理：文本补全、代码生成、格式化输出、质量控制、多候选、后处理
│   │   │   └── multimodal_inference.rs # 多模态推理：图文理解、音频处理、视频分析、跨模态融合、格式转换、结果整合
│   │   └── utils/                     # AI工具
│   │       ├── mod.rs                 # 工具模块：通用函数、辅助类、性能工具、调试工具、测试工具、基准测试
│   │       ├── prompt_template.rs     # 提示模板：模板引擎、变量替换、条件逻辑、循环处理、格式化、验证检查
│   │       ├── context_manager.rs     # 上下文管理：窗口管理、内存优化、压缩策略、相关性计算、历史管理、性能监控
│   │       ├── response_parser.rs     # 响应解析：格式解析、结构化提取、错误检测、质量评估、后处理、验证检查
│   │       └── performance_monitor.rs # 性能监控：推理时间、内存使用、GPU利用率、吞吐量、延迟分析、资源统计
│   ├── utils/                         # 工具模块
│   │   ├── mod.rs                     # 工具模块：通用工具、辅助函数、宏定义、类型转换、错误处理、性能工具
│   │   ├── config.rs                  # 配置管理：配置加载、环境变量、默认值、验证规则、热重载、配置合并
│   │   ├── logger.rs                  # 日志系统：日志配置、格式化、分级记录、文件轮转、性能监控、错误追踪
│   │   ├── crypto.rs                  # 加密工具：数据加密、哈希计算、签名验证、密钥管理、安全存储、随机数生成
│   │   ├── file_utils.rs              # 文件工具：文件操作、路径处理、权限管理、压缩解压、格式检测、批量处理
│   │   ├── network_utils.rs           # 网络工具：HTTP客户端、WebSocket、P2P通信、协议处理、连接管理、错误重试
│   │   ├── time_utils.rs              # 时间工具：时间格式化、时区处理、定时器、延迟执行、性能计时、日期计算
│   │   ├── validation.rs              # 验证工具：数据验证、格式检查、规则引擎、错误消息、自定义验证、批量验证
│   │   └── error.rs                   # 错误处理：错误定义、错误链、上下文信息、错误转换、日志记录、用户友好消息
│   ├── plugins/                       # 插件系统
│   │   ├── mod.rs                     # 插件模块：插件管理、生命周期、API代理、权限控制、沙箱执行、安全审计
│   │   ├── plugin_manager.rs          # 插件管理器：插件加载、卸载、更新、依赖管理、版本控制、冲突解决
│   │   ├── plugin_runtime.rs          # 插件运行时：WASM执行、资源限制、API调用、事件处理、错误隔离、性能监控
│   │   ├── plugin_api.rs              # 插件API：接口定义、权限检查、参数验证、返回值处理、版本兼容、文档生成
│   │   ├── security/                  # 安全模块
│   │   │   ├── mod.rs                 # 安全模块：权限管理、沙箱隔离、资源限制、审计日志、威胁检测、安全策略
│   │   │   ├── sandbox.rs             # 沙箱执行：进程隔离、资源限制、系统调用过滤、网络隔离、文件系统隔离、内存保护
│   │   │   ├── permissions.rs         # 权限系统：权限定义、检查机制、动态授权、权限继承、审计记录、违规处理
│   │   │   └── audit.rs               # 审计系统：操作记录、安全事件、风险评估、合规检查、报告生成、告警机制
│   │   └── store/                     # 插件商店
│   │       ├── mod.rs                 # 商店模块：插件发现、下载管理、版本控制、评价系统、推荐算法、安全扫描
│   │       ├── downloader.rs          # 下载器：断点续传、并发下载、完整性验证、签名检查、镜像切换、进度跟踪
│   │       ├── installer.rs           # 安装器：依赖解析、冲突检测、安装流程、回滚机制、权限设置、配置初始化
│   │       └── updater.rs             # 更新器：版本检查、增量更新、兼容性验证、自动更新、用户通知、回滚支持
│   ├── network/                       # 网络模块
│   │   ├── mod.rs                     # 网络模块：网络管理、协议处理、连接池、负载均衡、错误重试、性能监控
│   │   ├── p2p/                       # P2P通信
│   │   │   ├── mod.rs                 # P2P模块：节点发现、连接管理、消息路由、NAT穿透、安全通信、网络拓扑
│   │   │   ├── discovery.rs           # 设备发现：mDNS广播、局域网扫描、设备识别、服务注册、状态同步、连接历史
│   │   │   ├── connection.rs          # 连接管理：连接建立、心跳检测、重连机制、连接池、质量监控、故障转移
│   │   │   ├── protocol.rs            # 通信协议：消息格式、序列化、压缩传输、加密通信、版本协商、错误处理
│   │   │   └── nat_traversal.rs       # NAT穿透：STUN协议、TURN中继、UPnP映射、打洞技术、连接优化、兼容性处理
│   │   ├── sharing/                   # 资源共享
│   │   │   ├── mod.rs                 # 共享模块：资源管理、权限控制、传输优化、同步机制、冲突解决、版本控制
│   │   │   ├── file_sharing.rs        # 文件共享：文件传输、断点续传、完整性检查、权限验证、版本同步、冲突处理
│   │   │   ├── model_sharing.rs       # 模型共享：模型分发、版本管理、增量更新、权限控制、使用统计、质量评估
│   │   │   └── knowledge_sharing.rs   # 知识库共享：数据同步、增量更新、冲突解决、权限管理、版本控制、一致性保证
│   │   └── security/                  # 网络安全
│   │       ├── mod.rs                 # 安全模块：加密通信、身份认证、权限验证、攻击防护、审计日志、安全策略
│   │       ├── encryption.rs          # 加密通信：端到端加密、密钥交换、证书管理、加密算法、性能优化、兼容性处理
│   │       ├── authentication.rs      # 身份认证：用户验证、设备认证、令牌管理、多因素认证、单点登录、会话管理
│   │       └── firewall.rs            # 防火墙：访问控制、流量过滤、攻击检测、规则管理、日志记录、性能优化
│   └── tests/                         # 测试代码
│       ├── mod.rs                     # 测试模块：测试配置、工具函数、模拟数据、测试环境、性能基准、集成测试
│       ├── unit/                      # 单元测试
│       │   ├── mod.rs                 # 单元测试：测试组织、断言工具、模拟对象、测试数据、覆盖率、性能测试
│       │   ├── services/              # 服务测试：业务逻辑测试、边界条件、错误处理、性能测试、并发测试、集成测试
│       │   ├── models/                # 模型测试：数据验证、序列化、转换函数、边界值、错误情况、性能测试
│       │   └── utils/                 # 工具测试：函数测试、边界条件、错误处理、性能测试、兼容性、安全测试
│       ├── integration/               # 集成测试
│       │   ├── mod.rs                 # 集成测试：端到端测试、系统测试、接口测试、性能测试、压力测试、兼容性测试
│       │   ├── api_tests.rs           # API测试：接口测试、参数验证、返回值检查、错误处理、性能测试、安全测试
│       │   ├── database_tests.rs      # 数据库测试：CRUD操作、事务测试、并发测试、性能测试、数据一致性、备份恢复
│       │   └── ai_tests.rs            # AI测试：推理测试、模型加载、性能基准、质量评估、资源使用、错误处理
│       └── benchmarks/                # 性能基准
│           ├── mod.rs                 # 基准测试：性能测试、压力测试、负载测试、资源使用、瓶颈分析、优化建议
│           ├── inference_bench.rs     # 推理基准：推理速度、内存使用、GPU利用率、吞吐量、延迟分析、质量评估
│           ├── database_bench.rs      # 数据库基准：查询性能、写入速度、并发能力、索引效率、缓存命中、资源使用
│           └── network_bench.rs       # 网络基准：传输速度、连接延迟、并发连接、带宽利用、错误率、稳定性
```

### 3.2 Tauri集成与命令系统

#### 3.2.1 Tauri应用架构

AI Studio 基于 Tauri 2.x 构建，采用前后端分离的架构模式，通过 IPC（进程间通信）实现前端 Vue 应用与后端 Rust 服务的交互。

**Tauri架构图：**
```
Tauri应用架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Vue3)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   UI组件    │ │   状态管理   │ │   路由管理   │ │  工具库  │ │
│  │ Components  │ │    Pinia    │ │Vue Router   │ │ Utils   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ IPC通信
┌─────────────────────────────────────────────────────────────┐
│                      Tauri Bridge层                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 命令处理器   │ │  事件系统   │ │  权限管理   │ │ 错误处理 │ │
│  │ Commands    │ │   Events    │ │Permissions  │ │ Errors  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        后端层 (Rust)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  业务服务   │ │  数据访问   │ │  AI引擎     │ │ 系统集成 │ │
│  │  Services   │ │ Repository  │ │AI Engines   │ │ System  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**主应用入口 (main.rs)**
```rust
// src/main.rs
use tauri::{
    CustomMenuItem, Manager, Menu, MenuItem, Submenu, SystemTray, SystemTrayEvent,
    SystemTrayMenu, SystemTrayMenuItem, WindowBuilder, WindowUrl
};
use tokio::sync::Mutex;
use std::sync::Arc;

// 应用状态
#[derive(Default)]
pub struct AppState {
    pub chat_service: Arc<Mutex<ChatService>>,
    pub knowledge_service: Arc<Mutex<KnowledgeService>>,
    pub model_service: Arc<Mutex<ModelService>>,
    pub multimodal_service: Arc<Mutex<MultimodalService>>,
    pub network_service: Arc<Mutex<NetworkService>>,
    pub plugin_service: Arc<Mutex<PluginService>>,
    pub system_service: Arc<Mutex<SystemService>>,
}

#[tokio::main]
async fn main() {
    // 初始化日志系统
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
        .init();

    // 创建应用状态
    let app_state = AppState::default();

    // 创建菜单
    let menu = create_app_menu();

    // 创建系统托盘
    let tray = create_system_tray();

    // 构建Tauri应用
    tauri::Builder::default()
        .manage(app_state)
        .menu(menu)
        .system_tray(tray)
        .on_system_tray_event(handle_system_tray_event)
        .setup(|app| {
            // 应用初始化
            setup_app(app)?;
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 聊天命令
            chat::create_session,
            chat::send_message,
            chat::get_sessions,
            chat::get_messages,
            chat::delete_session,
            chat::update_session_settings,

            // 知识库命令
            knowledge::create_knowledge_base,
            knowledge::upload_documents,
            knowledge::search_documents,
            knowledge::get_knowledge_bases,
            knowledge::delete_knowledge_base,
            knowledge::get_embedding_progress,

            // 模型命令
            model::get_available_models,
            model::download_model,
            model::load_model,
            model::unload_model,
            model::get_model_status,
            model::get_download_progress,

            // 多模态命令
            multimodal::process_image,
            multimodal::process_audio,
            multimodal::process_video,
            multimodal::get_processing_history,

            // 网络命令
            network::discover_devices,
            network::connect_device,
            network::share_resource,
            network::get_connection_status,

            // 插件命令
            plugin::get_installed_plugins,
            plugin::install_plugin,
            plugin::uninstall_plugin,
            plugin::configure_plugin,
            plugin::get_plugin_store,

            // 系统命令
            system::get_system_info,
            system::get_performance_metrics,
            system::check_for_updates,
            system::export_logs,

            // 设置命令
            settings::get_settings,
            settings::update_settings,
            settings::reset_settings,
            settings::export_settings,
            settings::import_settings,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// 创建应用菜单
fn create_app_menu() -> Menu {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let close = CustomMenuItem::new("close".to_string(), "关闭");
    let minimize = CustomMenuItem::new("minimize".to_string(), "最小化");

    let submenu = Submenu::new("文件", Menu::new().add_item(quit).add_item(close));
    let window_submenu = Submenu::new("窗口", Menu::new().add_item(minimize));

    Menu::new()
        .add_submenu(submenu)
        .add_submenu(window_submenu)
        .add_native_item(MenuItem::Copy)
        .add_native_item(MenuItem::Paste)
        .add_native_item(MenuItem::Cut)
        .add_native_item(MenuItem::SelectAll)
}

// 创建系统托盘
fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let show = CustomMenuItem::new("show".to_string(), "显示");
    let hide = CustomMenuItem::new("hide".to_string(), "隐藏");

    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);

    SystemTray::new().with_menu(tray_menu)
}
```

#### 3.2.2 命令系统设计

**聊天命令实现 (commands/chat.rs)**
```rust
// src/commands/chat.rs
use tauri::{command, State};
use serde::{Deserialize, Serialize};
use crate::models::chat::{Session, Message, SessionSettings};
use crate::services::chat_service::ChatService;
use crate::utils::error::AppResult;

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateSessionRequest {
    pub title: Option<String>,
    pub model: String,
    pub settings: SessionSettings,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub content: String,
    pub attachments: Option<Vec<String>>,
}

// 创建新会话
#[command]
pub async fn create_session(
    request: CreateSessionRequest,
    state: State<'_, AppState>,
) -> AppResult<Session> {
    let chat_service = state.chat_service.lock().await;

    let session = chat_service.create_session(
        request.title,
        request.model,
        request.settings,
    ).await?;

    Ok(session)
}

// 发送消息
#[command]
pub async fn send_message(
    request: SendMessageRequest,
    state: State<'_, AppState>,
    window: tauri::Window,
) -> AppResult<Message> {
    let chat_service = state.chat_service.lock().await;

    // 创建用户消息
    let user_message = chat_service.add_user_message(
        &request.session_id,
        &request.content,
        request.attachments,
    ).await?;

    // 异步生成AI响应
    let session_id = request.session_id.clone();
    let content = request.content.clone();
    let service = chat_service.clone();
    let window_clone = window.clone();

    tokio::spawn(async move {
        match service.generate_ai_response(&session_id, &content).await {
            Ok(ai_message) => {
                // 通过事件发送AI响应
                let _ = window_clone.emit("ai_message_chunk", &ai_message);
            }
            Err(e) => {
                let _ = window_clone.emit("ai_message_error", &e.to_string());
            }
        }
    });

    Ok(user_message)
}

// 获取会话列表
#[command]
pub async fn get_sessions(
    state: State<'_, AppState>,
) -> AppResult<Vec<Session>> {
    let chat_service = state.chat_service.lock().await;
    let sessions = chat_service.get_sessions().await?;
    Ok(sessions)
}

// 获取会话消息
#[command]
pub async fn get_messages(
    session_id: String,
    page: Option<u32>,
    page_size: Option<u32>,
    state: State<'_, AppState>,
) -> AppResult<Vec<Message>> {
    let chat_service = state.chat_service.lock().await;
    let messages = chat_service.get_messages(
        &session_id,
        page.unwrap_or(1),
        page_size.unwrap_or(50),
    ).await?;
    Ok(messages)
}

// 删除会话
#[command]
pub async fn delete_session(
    session_id: String,
    state: State<'_, AppState>,
) -> AppResult<bool> {
    let chat_service = state.chat_service.lock().await;
    let result = chat_service.delete_session(&session_id).await?;
    Ok(result)
}

// 更新会话设置
#[command]
pub async fn update_session_settings(
    session_id: String,
    settings: SessionSettings,
    state: State<'_, AppState>,
) -> AppResult<Session> {
    let chat_service = state.chat_service.lock().await;
    let session = chat_service.update_session_settings(&session_id, settings).await?;
    Ok(session)
}
```

#### 3.2.3 事件系统

**事件发送机制**
```rust
// src/utils/events.rs
use tauri::{Manager, Window};
use serde::Serialize;

pub struct EventEmitter {
    window: Window,
}

impl EventEmitter {
    pub fn new(window: Window) -> Self {
        Self { window }
    }

    // 发送AI消息块（流式响应）
    pub fn emit_ai_message_chunk<T: Serialize>(&self, data: T) -> tauri::Result<()> {
        self.window.emit("ai_message_chunk", data)
    }

    // 发送下载进度
    pub fn emit_download_progress<T: Serialize>(&self, data: T) -> tauri::Result<()> {
        self.window.emit("download_progress", data)
    }

    // 发送系统通知
    pub fn emit_system_notification<T: Serialize>(&self, data: T) -> tauri::Result<()> {
        self.window.emit("system_notification", data)
    }

    // 发送错误事件
    pub fn emit_error<T: Serialize>(&self, data: T) -> tauri::Result<()> {
        self.window.emit("error", data)
    }

    // 发送状态更新
    pub fn emit_status_update<T: Serialize>(&self, data: T) -> tauri::Result<()> {
        self.window.emit("status_update", data)
    }
}
```

### 3.3 **AI推理引擎增强**

#### 3.3.1 本地/云端推理切换协议

AI Studio 支持本地推理和云端API的无缝切换，根据用户需求、模型可用性和性能要求自动选择最优的推理方式。

**推理引擎切换架构：**
```
推理引擎切换协议：

用户发起推理请求
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    推理调度器                                │
│ 1. 分析请求类型 → 2. 检查本地模型 → 3. 评估性能需求         │
│ 4. 检查网络状态 → 5. 考虑用户偏好 → 6. 选择推理引擎         │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────┐              ┌─────────────────┐
│   本地推理路径   │              │   云端推理路径   │
│                │              │                │
│ ┌─────────────┐ │              │ ┌─────────────┐ │
│ │Candle Engine│ │              │ │OpenAI API   │ │
│ └─────────────┘ │              │ └─────────────┘ │
│ ┌─────────────┐ │              │ ┌─────────────┐ │
│ │LLaMA.cpp    │ │              │ │Claude API   │ │
│ └─────────────┘ │              │ └─────────────┘ │
│ ┌─────────────┐ │              │ ┌─────────────┐ │
│ │ONNX Runtime │ │              │ │Gemini API   │ │
│ └─────────────┘ │              │ └─────────────┘ │
└─────────────────┘              └─────────────────┘
        ↓                                ↓
┌─────────────────────────────────────────────────────────────┐
│                    结果统一处理                              │
│ • 响应格式标准化 • 流式输出处理 • 错误统一处理 • 性能监控   │
└─────────────────────────────────────────────────────────────┘
```

**推理引擎管理器**
```rust
// src/ai/engines/engine_manager.rs
use std::collections::HashMap;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InferenceEngine {
    Local(LocalEngine),
    Remote(RemoteEngine),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LocalEngine {
    Candle,
    LlamaCpp,
    OnnxRuntime,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RemoteEngine {
    OpenAI,
    Claude,
    Gemini,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InferenceRequest {
    pub model: String,
    pub messages: Vec<Message>,
    pub temperature: f32,
    pub max_tokens: u32,
    pub stream: bool,
    pub engine_preference: Option<InferenceEngine>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InferenceResponse {
    pub content: String,
    pub tokens_used: u32,
    pub inference_time: u64,
    pub engine_used: InferenceEngine,
    pub cost: Option<f64>,
}

#[async_trait]
pub trait InferenceEngineInterface {
    async fn infer(&self, request: &InferenceRequest) -> Result<InferenceResponse, InferenceError>;
    async fn stream_infer(&self, request: &InferenceRequest) -> Result<InferenceStream, InferenceError>;
    fn is_available(&self) -> bool;
    fn get_supported_models(&self) -> Vec<String>;
    fn estimate_cost(&self, request: &InferenceRequest) -> Option<f64>;
}

pub struct InferenceEngineManager {
    engines: HashMap<InferenceEngine, Box<dyn InferenceEngineInterface + Send + Sync>>,
    fallback_order: Vec<InferenceEngine>,
    performance_metrics: HashMap<InferenceEngine, PerformanceMetrics>,
}

impl InferenceEngineManager {
    pub fn new() -> Self {
        Self {
            engines: HashMap::new(),
            fallback_order: vec![
                InferenceEngine::Local(LocalEngine::Candle),
                InferenceEngine::Local(LocalEngine::LlamaCpp),
                InferenceEngine::Remote(RemoteEngine::OpenAI),
            ],
            performance_metrics: HashMap::new(),
        }
    }

    // 智能选择推理引擎
    pub async fn select_engine(&self, request: &InferenceRequest) -> InferenceEngine {
        // 1. 检查用户偏好
        if let Some(preferred) = &request.engine_preference {
            if self.is_engine_available(preferred) {
                return preferred.clone();
            }
        }

        // 2. 根据模型选择引擎
        for engine in &self.fallback_order {
            if self.is_engine_available(engine) &&
               self.supports_model(engine, &request.model) {
                return engine.clone();
            }
        }

        // 3. 默认回退
        self.fallback_order[0].clone()
    }

    // 执行推理
    pub async fn infer(&self, request: &InferenceRequest) -> Result<InferenceResponse, InferenceError> {
        let engine = self.select_engine(request).await;

        if let Some(engine_impl) = self.engines.get(&engine) {
            let start_time = std::time::Instant::now();
            let result = engine_impl.infer(request).await;
            let inference_time = start_time.elapsed().as_millis() as u64;

            // 更新性能指标
            self.update_performance_metrics(&engine, inference_time, result.is_ok());

            result
        } else {
            Err(InferenceError::EngineNotAvailable(engine))
        }
    }

    // 流式推理
    pub async fn stream_infer(&self, request: &InferenceRequest) -> Result<InferenceStream, InferenceError> {
        let engine = self.select_engine(request).await;

        if let Some(engine_impl) = self.engines.get(&engine) {
            engine_impl.stream_infer(request).await
        } else {
            Err(InferenceError::EngineNotAvailable(engine))
        }
    }

    fn is_engine_available(&self, engine: &InferenceEngine) -> bool {
        self.engines.get(engine)
            .map(|e| e.is_available())
            .unwrap_or(false)
    }

    fn supports_model(&self, engine: &InferenceEngine, model: &str) -> bool {
        self.engines.get(engine)
            .map(|e| e.get_supported_models().contains(&model.to_string()))
            .unwrap_or(false)
    }
}
```

#### 3.3.2 多引擎调度时序图

```
多引擎调度时序图：

用户请求 → 调度器 → 引擎选择 → 推理执行 → 结果返回

时间轴：
T0: 用户发起推理请求
    │
    ├─ 请求解析和验证
    │
T1: 调度器接收请求
    │
    ├─ 检查本地模型状态
    ├─ 评估网络连接质量
    ├─ 分析请求复杂度
    ├─ 查询性能历史数据
    │
T2: 引擎选择决策
    │
    ├─ 优先级排序
    ├─ 可用性检查
    ├─ 成本评估
    │
T3: 推理引擎初始化
    │
    ├─ 模型加载（如需要）
    ├─ 上下文准备
    ├─ 参数配置
    │
T4: 推理执行开始
    │
    ├─ 输入预处理
    ├─ 推理计算
    ├─ 流式输出（如启用）
    │
T5: 推理完成
    │
    ├─ 结果后处理
    ├─ 格式标准化
    ├─ 性能指标记录
    │
T6: 结果返回用户

并发处理：
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   引擎A (本地)   │  │   引擎B (本地)   │  │   引擎C (云端)   │
│                │  │                │  │                │
│ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
│ │模型1 (7B)   │ │  │ │模型2 (13B)  │ │  │ │GPT-4 API    │ │
│ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
│ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │
│ │模型3 (量化) │ │  │ │模型4 (专用) │ │  │ │Claude API   │ │
│ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │
└─────────────────┘  └─────────────────┘  └─────────────────┘
        ↓                      ↓                      ↓
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡器                                │
│ • 请求分发 • 队列管理 • 故障转移 • 性能监控                 │
└─────────────────────────────────────────────────────────────┘
```

#### 3.3.3 量化模型热加载机制

```rust
// src/ai/models/quantization.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Debug, Clone)]
pub enum QuantizationType {
    FP16,
    INT8,
    INT4,
    GPTQ,
    AWQ,
    GGUF,
}

#[derive(Debug, Clone)]
pub struct QuantizedModel {
    pub model_id: String,
    pub quantization_type: QuantizationType,
    pub file_path: String,
    pub memory_usage: u64,
    pub load_time: u64,
    pub accuracy_score: f32,
    pub is_loaded: bool,
}

pub struct ModelHotLoader {
    loaded_models: Arc<RwLock<HashMap<String, QuantizedModel>>>,
    memory_limit: u64,
    cache_strategy: CacheStrategy,
    preload_queue: Arc<RwLock<Vec<String>>>,
}

impl ModelHotLoader {
    pub fn new(memory_limit: u64) -> Self {
        Self {
            loaded_models: Arc::new(RwLock::new(HashMap::new())),
            memory_limit,
            cache_strategy: CacheStrategy::LRU,
            preload_queue: Arc::new(RwLock::new(Vec::new())),
        }
    }

    // 热加载模型
    pub async fn hot_load_model(&self, model_id: &str) -> Result<(), ModelError> {
        let mut models = self.loaded_models.write().await;

        // 检查是否已加载
        if models.contains_key(model_id) {
            return Ok(());
        }

        // 检查内存限制
        let current_memory = self.calculate_memory_usage(&models).await;
        let model_info = self.get_model_info(model_id).await?;

        if current_memory + model_info.memory_usage > self.memory_limit {
            // 释放最少使用的模型
            self.evict_models(&mut models, model_info.memory_usage).await?;
        }

        // 加载模型
        let start_time = std::time::Instant::now();
        let loaded_model = self.load_quantized_model(model_id).await?;
        let load_time = start_time.elapsed().as_millis() as u64;

        let mut model = loaded_model;
        model.load_time = load_time;
        model.is_loaded = true;

        models.insert(model_id.to_string(), model);

        // 预加载相关模型
        self.schedule_preload(model_id).await;

        Ok(())
    }

    // 智能预加载
    async fn schedule_preload(&self, current_model: &str) {
        let related_models = self.get_related_models(current_model).await;
        let mut preload_queue = self.preload_queue.write().await;

        for model in related_models {
            if !preload_queue.contains(&model) {
                preload_queue.push(model);
            }
        }

        // 异步预加载
        let queue_clone = self.preload_queue.clone();
        let loader_clone = self.clone();

        tokio::spawn(async move {
            let mut queue = queue_clone.write().await;
            while let Some(model_id) = queue.pop() {
                if let Err(e) = loader_clone.hot_load_model(&model_id).await {
                    eprintln!("Failed to preload model {}: {}", model_id, e);
                }

                // 避免过度预加载
                tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
            }
        });
    }

    // 模型卸载策略
    async fn evict_models(&self, models: &mut HashMap<String, QuantizedModel>, needed_memory: u64) -> Result<(), ModelError> {
        let mut candidates: Vec<_> = models.iter().collect();

        // 根据策略排序
        match self.cache_strategy {
            CacheStrategy::LRU => {
                candidates.sort_by_key(|(_, model)| model.load_time);
            }
            CacheStrategy::LFU => {
                // 基于使用频率排序
                candidates.sort_by_key(|(_, model)| model.accuracy_score as u64);
            }
            CacheStrategy::Size => {
                candidates.sort_by_key(|(_, model)| model.memory_usage);
            }
        }

        let mut freed_memory = 0u64;
        let mut to_remove = Vec::new();

        for (model_id, model) in candidates {
            if freed_memory >= needed_memory {
                break;
            }

            freed_memory += model.memory_usage;
            to_remove.push(model_id.clone());

            // 卸载模型
            self.unload_model(model_id).await?;
        }

        for model_id in to_remove {
            models.remove(&model_id);
        }

        Ok(())
    }
}

#[derive(Debug, Clone)]
enum CacheStrategy {
    LRU,  // 最近最少使用
    LFU,  // 最少使用频率
    Size, // 按大小优先
}
```

---

## 第四部分：核心功能模块

### 4.1 聊天功能模块

#### 4.1.1 聊天服务架构

AI Studio 的聊天功能是整个应用的核心，提供智能对话、多模态交互、上下文管理等完整的AI助手体验。

**聊天模块架构图：**
```
聊天功能模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端聊天界面                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 会话列表    │ │ 消息显示    │ │ 输入组件    │ │ 设置面板 │ │
│  │SessionList  │ │MessageList  │ │MessageInput │ │Settings │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ IPC调用
┌─────────────────────────────────────────────────────────────┐
│                        聊天命令层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 会话管理    │ │ 消息处理    │ │ 流式响应    │ │ 设置管理 │ │
│  │SessionCmd   │ │MessageCmd   │ │StreamCmd    │ │ConfigCmd │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        聊天服务层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 会话服务    │ │ 消息服务    │ │ AI推理服务  │ │ 上下文   │ │
│  │SessionSvc   │ │MessageSvc   │ │InferenceSvc │ │ContextMgr│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 会话存储    │ │ 消息存储    │ │ 配置存储    │ │ 缓存层   │ │
│  │SessionRepo  │ │MessageRepo  │ │ConfigRepo   │ │CacheLayer│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**聊天服务实现**
```rust
// src/services/chat_service.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use uuid::Uuid;

use crate::models::chat::{Session, Message, SessionSettings, MessageRole};
use crate::database::repositories::chat_repository::ChatRepository;
use crate::ai::inference::chat_inference::ChatInference;
use crate::utils::error::{AppResult, AppError};

pub struct ChatService {
    repository: Arc<ChatRepository>,
    inference_engine: Arc<ChatInference>,
    active_sessions: Arc<RwLock<HashMap<String, SessionContext>>>,
    message_cache: Arc<Mutex<HashMap<String, Vec<Message>>>>,
}

#[derive(Debug, Clone)]
pub struct SessionContext {
    pub session: Session,
    pub message_count: usize,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub context_window: Vec<Message>,
    pub is_generating: bool,
}

impl ChatService {
    pub fn new(repository: Arc<ChatRepository>, inference_engine: Arc<ChatInference>) -> Self {
        Self {
            repository,
            inference_engine,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            message_cache: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    // 创建新会话
    pub async fn create_session(
        &self,
        title: Option<String>,
        model: String,
        settings: SessionSettings,
    ) -> AppResult<Session> {
        let session_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now();

        let session = Session {
            id: session_id.clone(),
            title: title.unwrap_or_else(|| format!("新对话 {}", now.format("%m-%d %H:%M"))),
            created_at: now,
            updated_at: now,
            message_count: 0,
            model,
            settings,
            tags: Vec::new(),
            is_archived: false,
        };

        // 保存到数据库
        self.repository.create_session(&session).await?;

        // 添加到活跃会话
        let context = SessionContext {
            session: session.clone(),
            message_count: 0,
            last_activity: now,
            context_window: Vec::new(),
            is_generating: false,
        };

        let mut active_sessions = self.active_sessions.write().await;
        active_sessions.insert(session_id, context);

        Ok(session)
    }

    // 发送消息
    pub async fn send_message(
        &self,
        session_id: &str,
        content: &str,
        attachments: Option<Vec<String>>,
    ) -> AppResult<Message> {
        // 创建用户消息
        let user_message = Message {
            id: Uuid::new_v4().to_string(),
            session_id: session_id.to_string(),
            content: content.to_string(),
            role: MessageRole::User,
            timestamp: chrono::Utc::now(),
            status: crate::models::chat::MessageStatus::Sent,
            metadata: Some(serde_json::json!({
                "attachments": attachments.unwrap_or_default()
            })),
        };

        // 保存用户消息
        self.repository.save_message(&user_message).await?;

        // 更新会话上下文
        self.update_session_context(session_id, &user_message).await?;

        // 异步生成AI响应
        let session_id_clone = session_id.to_string();
        let content_clone = content.to_string();
        let service_clone = Arc::new(self.clone());

        tokio::spawn(async move {
            if let Err(e) = service_clone.generate_ai_response(&session_id_clone, &content_clone).await {
                eprintln!("Failed to generate AI response: {}", e);
            }
        });

        Ok(user_message)
    }

    // 生成AI响应
    pub async fn generate_ai_response(&self, session_id: &str, user_input: &str) -> AppResult<Message> {
        // 获取会话上下文
        let context = self.get_session_context(session_id).await?;

        // 标记正在生成
        self.set_generating_status(session_id, true).await?;

        // 准备推理请求
        let inference_request = self.prepare_inference_request(&context, user_input).await?;

        // 执行推理
        let ai_response = self.inference_engine.generate_response(inference_request).await?;

        // 创建AI消息
        let ai_message = Message {
            id: Uuid::new_v4().to_string(),
            session_id: session_id.to_string(),
            content: ai_response.content,
            role: MessageRole::Assistant,
            timestamp: chrono::Utc::now(),
            status: crate::models::chat::MessageStatus::Sent,
            metadata: Some(serde_json::json!({
                "model": context.session.model,
                "tokens_used": ai_response.tokens_used,
                "inference_time": ai_response.inference_time,
                "temperature": context.session.settings.temperature
            })),
        };

        // 保存AI消息
        self.repository.save_message(&ai_message).await?;

        // 更新会话上下文
        self.update_session_context(session_id, &ai_message).await?;

        // 取消生成状态
        self.set_generating_status(session_id, false).await?;

        Ok(ai_message)
    }

    // 获取会话列表
    pub async fn get_sessions(&self, page: u32, page_size: u32) -> AppResult<Vec<Session>> {
        self.repository.get_sessions(page, page_size).await
    }

    // 获取会话消息
    pub async fn get_messages(&self, session_id: &str, page: u32, page_size: u32) -> AppResult<Vec<Message>> {
        // 先检查缓存
        let cache = self.message_cache.lock().await;
        if let Some(cached_messages) = cache.get(session_id) {
            let start = ((page - 1) * page_size) as usize;
            let end = (start + page_size as usize).min(cached_messages.len());

            if start < cached_messages.len() {
                return Ok(cached_messages[start..end].to_vec());
            }
        }
        drop(cache);

        // 从数据库获取
        let messages = self.repository.get_messages(session_id, page, page_size).await?;

        // 更新缓存
        let mut cache = self.message_cache.lock().await;
        cache.insert(session_id.to_string(), messages.clone());

        Ok(messages)
    }
}
```

### 4.2 **知识库系统增强**

#### 4.2.1 文档解析流程图

```
文档解析完整流程：

文件上传 → 格式检测 → 内容提取 → 文本清理 → 智能分块 → 向量化 → 索引存储

详细流程：
┌─────────────────────────────────────────────────────────────┐
│                    文件上传与验证                            │
│ 1. 文件格式检查 → 2. 文件大小验证 → 3. 重复文件检测         │
│ 4. 病毒扫描 → 5. 权限验证 → 6. 存储空间检查                 │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    内容提取处理                              │
│                                                             │
│ [PDF文档] → PDFium/PyPDF2 → 文本+图片+表格                 │
│     ↓                                                       │
│ OCR识别 → 文字提取 → 布局分析 → 结构化数据                 │
│                                                             │
│ [Word文档] → python-docx → 文本+样式+媒体                  │
│     ↓                                                       │
│ 样式保留 → 结构解析 → 元数据提取 → 格式转换                │
│                                                             │
│ [Excel文档] → openpyxl → 表格数据+公式                     │
│     ↓                                                       │
│ 工作表遍历 → 数据类型识别 → 关系分析 → 结构化存储           │
│                                                             │
│ [Markdown] → markdown-it → HTML → 纯文本                   │
│     ↓                                                       │
│ 语法解析 → 链接提取 → 代码块处理 → 格式标准化               │
│                                                             │
│ [HTML网页] → BeautifulSoup → 内容提取                      │
│     ↓                                                       │
│ 标签清理 → 正文提取 → 链接处理 → 元数据解析                │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    文本预处理                                │
│ • 编码检测和转换 • 特殊字符清理 • 空白字符标准化             │
│ • 重复内容去除 • 噪声过滤 • 语言检测                       │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    智能分块策略                              │
│                                                             │
│ [语义分块] → 句子边界检测 → 段落分析 → 主题连贯性           │
│     ↓                                                       │
│ 滑动窗口 → 重叠处理 → 上下文保持 → 分块质量评估             │
│                                                             │
│ [固定长度分块] → 字符计数 → 边界调整 → 完整性保证           │
│     ↓                                                       │
│ 分块大小优化 → 重叠区域设置 → 索引标记 → 关联关系           │
│                                                             │
│ [结构化分块] → 标题识别 → 章节划分 → 层次结构               │
│     ↓                                                       │
│ 逻辑单元保持 → 引用关系 → 交叉索引 → 元数据关联             │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    向量化处理                                │
│ • Embedding模型选择 • 批量向量化 • 维度优化                 │
│ • 相似度计算 • 质量评估 • 异常检测                         │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    索引存储                                  │
│ • ChromaDB存储 • 索引优化 • 元数据关联                     │
│ • 备份机制 • 版本控制 • 性能监控                           │
└─────────────────────────────────────────────────────────────┘
```

#### 4.2.2 向量检索优化矩阵

| 优化维度 | 策略 | 性能提升 | 内存占用 | 实现复杂度 |
|---------|------|----------|----------|------------|
| **索引结构** | | | | |
| HNSW | 分层导航小世界图 | 95% | 高 | 中 |
| IVF | 倒排文件索引 | 85% | 中 | 低 |
| LSH | 局部敏感哈希 | 75% | 低 | 中 |
| **向量压缩** | | | | |
| PQ | 乘积量化 | 80% | 极低 | 高 |
| SQ | 标量量化 | 70% | 低 | 低 |
| OPQ | 优化乘积量化 | 85% | 低 | 高 |
| **搜索优化** | | | | |
| 预过滤 | 元数据过滤 | 60% | 无 | 低 |
| 重排序 | 精确计算 | 40% | 无 | 中 |
| 缓存 | 热点数据缓存 | 90% | 中 | 低 |
| **并行化** | | | | |
| 多线程 | CPU并行搜索 | 300% | 无 | 中 |
| GPU加速 | CUDA/OpenCL | 1000% | 高 | 高 |
| 分布式 | 集群搜索 | 500% | 分布 | 高 |

**知识库服务实现**
```rust
// src/services/knowledge_service.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};

use crate::models::knowledge::{KnowledgeBase, Document, SearchResult, EmbeddingProgress};
use crate::database::repositories::knowledge_repository::KnowledgeRepository;
use crate::database::chroma::ChromaClient;
use crate::ai::inference::embedding_inference::EmbeddingInference;

pub struct KnowledgeService {
    repository: Arc<KnowledgeRepository>,
    chroma_client: Arc<ChromaClient>,
    embedding_engine: Arc<EmbeddingInference>,
    processing_queue: Arc<Mutex<Vec<ProcessingTask>>>,
    active_processors: Arc<RwLock<HashMap<String, ProcessingStatus>>>,
}

#[derive(Debug, Clone)]
pub struct ProcessingTask {
    pub task_id: String,
    pub knowledge_base_id: String,
    pub document_path: String,
    pub chunk_strategy: ChunkStrategy,
    pub priority: u8,
}

#[derive(Debug, Clone)]
pub enum ChunkStrategy {
    FixedSize { size: usize, overlap: usize },
    Semantic { max_size: usize, min_size: usize },
    Structural { preserve_hierarchy: bool },
    Hybrid { primary: Box<ChunkStrategy>, fallback: Box<ChunkStrategy> },
}

impl KnowledgeService {
    // 创建知识库
    pub async fn create_knowledge_base(
        &self,
        name: &str,
        description: Option<&str>,
        embedding_model: &str,
        chunk_strategy: ChunkStrategy,
    ) -> AppResult<KnowledgeBase> {
        let kb_id = Uuid::new_v4().to_string();

        let knowledge_base = KnowledgeBase {
            id: kb_id.clone(),
            name: name.to_string(),
            description: description.map(|s| s.to_string()),
            embedding_model: embedding_model.to_string(),
            chunk_strategy,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            document_count: 0,
            total_chunks: 0,
            storage_size: 0,
        };

        // 保存到数据库
        self.repository.create_knowledge_base(&knowledge_base).await?;

        // 在ChromaDB中创建集合
        self.chroma_client.create_collection(&kb_id, embedding_model).await?;

        Ok(knowledge_base)
    }

    // 上传文档
    pub async fn upload_documents(
        &self,
        knowledge_base_id: &str,
        file_paths: Vec<String>,
        chunk_strategy: Option<ChunkStrategy>,
    ) -> AppResult<Vec<String>> {
        let mut task_ids = Vec::new();

        for file_path in file_paths {
            let task_id = Uuid::new_v4().to_string();

            let task = ProcessingTask {
                task_id: task_id.clone(),
                knowledge_base_id: knowledge_base_id.to_string(),
                document_path: file_path,
                chunk_strategy: chunk_strategy.clone().unwrap_or(ChunkStrategy::Semantic {
                    max_size: 1000,
                    min_size: 100,
                }),
                priority: 5,
            };

            // 添加到处理队列
            let mut queue = self.processing_queue.lock().await;
            queue.push(task);
            queue.sort_by_key(|t| std::cmp::Reverse(t.priority));

            task_ids.push(task_id);
        }

        // 启动处理器
        self.start_document_processor().await;

        Ok(task_ids)
    }

    // 文档处理器
    async fn start_document_processor(&self) {
        let queue = self.processing_queue.clone();
        let processors = self.active_processors.clone();
        let service = Arc::new(self.clone());

        tokio::spawn(async move {
            loop {
                let task = {
                    let mut queue_guard = queue.lock().await;
                    queue_guard.pop()
                };

                if let Some(task) = task {
                    // 标记为处理中
                    {
                        let mut processors_guard = processors.write().await;
                        processors_guard.insert(task.task_id.clone(), ProcessingStatus::Processing);
                    }

                    // 处理文档
                    match service.process_document(&task).await {
                        Ok(_) => {
                            let mut processors_guard = processors.write().await;
                            processors_guard.insert(task.task_id, ProcessingStatus::Completed);
                        }
                        Err(e) => {
                            eprintln!("Failed to process document: {}", e);
                            let mut processors_guard = processors.write().await;
                            processors_guard.insert(task.task_id, ProcessingStatus::Failed(e.to_string()));
                        }
                    }
                } else {
                    // 队列为空，等待
                    tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
                }
            }
        });
    }
}

#[derive(Debug, Clone)]
pub enum ProcessingStatus {
    Pending,
    Processing,
    Completed,
    Failed(String),
}
```

#### 4.2.3 知识图谱关系映射

```
知识图谱构建流程：

文档实体识别 → 关系抽取 → 图谱构建 → 关系推理 → 可视化展示

实体关系图谱：
┌─────────────────────────────────────────────────────────────┐
│                    实体识别层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   人物实体   │ │   组织实体   │ │   概念实体   │ │ 事件实体 │ │
│  │   Person    │ │Organization │ │   Concept   │ │  Event  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    关系抽取层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   从属关系   │ │   因果关系   │ │   时序关系   │ │ 相似关系 │ │
│  │  BelongsTo  │ │  CausedBy   │ │  Temporal   │ │ Similar │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    图谱存储层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   节点存储   │ │   边存储    │ │   属性存储   │ │ 索引优化 │ │
│  │   Nodes     │ │   Edges     │ │ Properties  │ │ Indexes │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘

知识图谱查询优化：
┌─────────────────────────────────────────────────────────────┐
│                    查询类型                                  │
│                                                             │
│ [路径查询] → 最短路径 → 多跳关系 → 路径权重                 │
│     ↓                                                       │
│ Dijkstra算法 → A*搜索 → 双向BFS → 路径排序                 │
│                                                             │
│ [子图查询] → 模式匹配 → 结构相似 → 子图同构                 │
│     ↓                                                       │
│ 图匹配算法 → VF2算法 → 近似匹配 → 相似度计算               │
│                                                             │
│ [聚合查询] → 节点统计 → 关系分析 → 中心性计算               │
│     ↓                                                       │
│ PageRank → 度中心性 → 介数中心性 → 聚类系数                │
│                                                             │
│ [推理查询] → 规则推理 → 传递闭包 → 关系推导                 │
│     ↓                                                       │
│ 逻辑推理 → 概率推理 → 图神经网络 → 知识补全                │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 模型管理模块

#### 4.3.1 模型生命周期管理

```
模型生命周期管理流程：

发现 → 下载 → 验证 → 部署 → 监控 → 更新 → 卸载

详细管理流程：
┌─────────────────────────────────────────────────────────────┐
│                    模型发现阶段                              │
│ 1. HuggingFace Hub扫描 → 2. 本地模型检测 → 3. 自定义源     │
│ 4. 版本比较 → 5. 兼容性检查 → 6. 推荐算法                   │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    模型下载阶段                              │
│                                                             │
│ [下载管理器] → 多线程下载 → 断点续传 → 完整性验证           │
│     ↓                                                       │
│ 镜像源切换 → 网络优化 → 进度跟踪 → 错误重试                │
│                                                             │
│ [存储管理] → 路径规划 → 空间检查 → 重复检测                 │
│     ↓                                                       │
│ 压缩存储 → 版本管理 → 清理策略 → 备份机制                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    模型验证阶段                              │
│ • 文件完整性校验 • 格式兼容性检查 • 安全扫描               │
│ • 性能基准测试 • 质量评估 • 依赖检查                       │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    模型部署阶段                              │
│                                                             │
│ [环境准备] → 依赖安装 → 配置设置 → 资源分配                │
│     ↓                                                       │
│ 内存预分配 → GPU设备选择 → 并发控制 → 预热处理             │
│                                                             │
│ [服务启动] → 模型加载 → 接口注册 → 健康检查                 │
│     ↓                                                       │
│ 负载均衡 → 故障转移 → 监控接入 → 日志配置                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    运行监控阶段                              │
│ • 性能指标监控 • 资源使用统计 • 错误率跟踪                 │
│ • 用户反馈收集 • 质量评估 • 优化建议                       │
└─────────────────────────────────────────────────────────────┘
```

**模型管理服务实现**
```rust
// src/services/model_service.rs
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};

use crate::models::model::{ModelInfo, ModelStatus, DownloadProgress, ModelMetrics};
use crate::database::repositories::model_repository::ModelRepository;
use crate::utils::downloader::ModelDownloader;
use crate::ai::models::model_manager::ModelManager;

pub struct ModelService {
    repository: Arc<ModelRepository>,
    downloader: Arc<ModelDownloader>,
    model_manager: Arc<ModelManager>,
    download_tasks: Arc<RwLock<HashMap<String, DownloadTask>>>,
    loaded_models: Arc<RwLock<HashMap<String, LoadedModel>>>,
    performance_metrics: Arc<Mutex<HashMap<String, ModelMetrics>>>,
}

#[derive(Debug, Clone)]
pub struct DownloadTask {
    pub model_id: String,
    pub url: String,
    pub local_path: PathBuf,
    pub progress: DownloadProgress,
    pub status: DownloadStatus,
}

#[derive(Debug, Clone)]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Paused,
    Completed,
    Failed(String),
    Cancelled,
}

#[derive(Debug, Clone)]
pub struct LoadedModel {
    pub model_info: ModelInfo,
    pub load_time: chrono::DateTime<chrono::Utc>,
    pub memory_usage: u64,
    pub inference_count: u64,
    pub average_latency: f64,
    pub error_count: u64,
}

impl ModelService {
    pub fn new(
        repository: Arc<ModelRepository>,
        downloader: Arc<ModelDownloader>,
        model_manager: Arc<ModelManager>,
    ) -> Self {
        Self {
            repository,
            downloader,
            model_manager,
            download_tasks: Arc::new(RwLock::new(HashMap::new())),
            loaded_models: Arc::new(RwLock::new(HashMap::new())),
            performance_metrics: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    // 获取可用模型列表
    pub async fn get_available_models(&self) -> AppResult<Vec<ModelInfo>> {
        // 合并本地模型和远程模型
        let mut models = Vec::new();

        // 本地模型
        let local_models = self.repository.get_local_models().await?;
        models.extend(local_models);

        // 远程模型（缓存1小时）
        let remote_models = self.get_remote_models_cached().await?;
        models.extend(remote_models);

        // 去重和排序
        models.sort_by(|a, b| a.name.cmp(&b.name));
        models.dedup_by(|a, b| a.id == b.id);

        Ok(models)
    }

    // 下载模型
    pub async fn download_model(
        &self,
        model_id: &str,
        variant: Option<&str>,
        local_path: Option<PathBuf>,
    ) -> AppResult<String> {
        let model_info = self.get_model_info(model_id).await?;
        let download_url = self.resolve_download_url(&model_info, variant).await?;

        let task_id = Uuid::new_v4().to_string();
        let target_path = local_path.unwrap_or_else(|| {
            self.get_default_model_path(&model_info)
        });

        let task = DownloadTask {
            model_id: model_id.to_string(),
            url: download_url,
            local_path: target_path.clone(),
            progress: DownloadProgress::default(),
            status: DownloadStatus::Pending,
        };

        // 添加到下载任务
        {
            let mut tasks = self.download_tasks.write().await;
            tasks.insert(task_id.clone(), task);
        }

        // 启动下载
        let downloader = self.downloader.clone();
        let tasks = self.download_tasks.clone();
        let task_id_clone = task_id.clone();

        tokio::spawn(async move {
            match downloader.download_with_progress(&download_url, &target_path).await {
                Ok(_) => {
                    let mut tasks_guard = tasks.write().await;
                    if let Some(task) = tasks_guard.get_mut(&task_id_clone) {
                        task.status = DownloadStatus::Completed;
                        task.progress.percentage = 100.0;
                    }
                }
                Err(e) => {
                    let mut tasks_guard = tasks.write().await;
                    if let Some(task) = tasks_guard.get_mut(&task_id_clone) {
                        task.status = DownloadStatus::Failed(e.to_string());
                    }
                }
            }
        });

        Ok(task_id)
    }

    // 加载模型
    pub async fn load_model(&self, model_id: &str) -> AppResult<()> {
        let model_info = self.repository.get_model_by_id(model_id).await?;

        // 检查是否已加载
        {
            let loaded = self.loaded_models.read().await;
            if loaded.contains_key(model_id) {
                return Ok(());
            }
        }

        // 加载模型
        let start_time = std::time::Instant::now();
        self.model_manager.load_model(&model_info).await?;
        let load_duration = start_time.elapsed();

        // 获取内存使用情况
        let memory_usage = self.model_manager.get_model_memory_usage(model_id).await?;

        // 记录加载信息
        let loaded_model = LoadedModel {
            model_info,
            load_time: chrono::Utc::now(),
            memory_usage,
            inference_count: 0,
            average_latency: 0.0,
            error_count: 0,
        };

        {
            let mut loaded = self.loaded_models.write().await;
            loaded.insert(model_id.to_string(), loaded_model);
        }

        // 更新状态
        self.repository.update_model_status(model_id, ModelStatus::Loaded).await?;

        Ok(())
    }

    // 获取模型性能指标
    pub async fn get_model_metrics(&self, model_id: &str) -> AppResult<ModelMetrics> {
        let metrics = self.performance_metrics.lock().await;

        if let Some(model_metrics) = metrics.get(model_id) {
            Ok(model_metrics.clone())
        } else {
            // 生成默认指标
            Ok(ModelMetrics::default())
        }
    }

    // 更新性能指标
    pub async fn update_performance_metrics(
        &self,
        model_id: &str,
        inference_time: u64,
        success: bool,
    ) {
        let mut metrics = self.performance_metrics.lock().await;
        let model_metrics = metrics.entry(model_id.to_string()).or_insert_with(ModelMetrics::default);

        model_metrics.total_inferences += 1;
        model_metrics.total_inference_time += inference_time;
        model_metrics.average_latency = model_metrics.total_inference_time as f64 / model_metrics.total_inferences as f64;

        if !success {
            model_metrics.error_count += 1;
        }

        model_metrics.error_rate = model_metrics.error_count as f64 / model_metrics.total_inferences as f64;
        model_metrics.last_updated = chrono::Utc::now();
    }
}
```

---

## 第五部分：数据层设计

### 5.1 SQLite关系型数据库

#### 5.1.1 数据库架构设计

AI Studio 使用 SQLite 作为主要的关系型数据库，存储应用的结构化数据，包括用户会话、系统配置、模型信息等。

**数据库架构图：**
```
SQLite数据库架构：

┌─────────────────────────────────────────────────────────────┐
│                        应用数据库                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   用户数据   │ │   会话数据   │ │   消息数据   │ │ 配置数据 │ │
│  │    Users    │ │  Sessions   │ │  Messages   │ │Settings │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   模型数据   │ │  知识库数据  │ │   插件数据   │ │ 系统日志 │ │
│  │   Models    │ │ KnowledgeDB │ │   Plugins   │ │  Logs   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        索引优化                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   主键索引   │ │   外键索引   │ │   复合索引   │ │ 全文索引 │ │
│  │Primary Keys │ │Foreign Keys │ │Composite Idx│ │FTS Index│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        性能优化                              │
│ • WAL模式启用 • 连接池管理 • 查询优化 • 事务控制             │
│ • 备份策略 • 数据迁移 • 完整性检查 • 性能监控               │
└─────────────────────────────────────────────────────────────┘
```

**数据库表结构设计**
```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password_hash TEXT,
    display_name TEXT,
    avatar_url TEXT,
    preferences TEXT, -- JSON格式的用户偏好
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE
);

-- 会话表
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    model TEXT NOT NULL,
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2048,
    top_p REAL DEFAULT 0.9,
    frequency_penalty REAL DEFAULT 0.0,
    presence_penalty REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    message_count INTEGER DEFAULT 0,
    tags TEXT, -- JSON数组
    is_archived BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 消息表
CREATE TABLE messages (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    content_type TEXT DEFAULT 'text',
    metadata TEXT, -- JSON格式的元数据
    tokens_used INTEGER,
    inference_time INTEGER, -- 毫秒
    model_used TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'sent' CHECK (status IN ('sending', 'sent', 'error')),
    parent_message_id TEXT,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_message_id) REFERENCES messages(id)
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT NOT NULL,
    chunk_size INTEGER DEFAULT 1000,
    chunk_overlap INTEGER DEFAULT 200,
    chunk_strategy TEXT DEFAULT 'semantic',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    document_count INTEGER DEFAULT 0,
    total_chunks INTEGER DEFAULT 0,
    storage_size INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    knowledge_base_id TEXT NOT NULL,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    mime_type TEXT,
    content_hash TEXT NOT NULL,
    chunk_count INTEGER DEFAULT 0,
    processing_status TEXT DEFAULT 'pending' CHECK (
        processing_status IN ('pending', 'processing', 'completed', 'failed')
    ),
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    metadata TEXT, -- JSON格式
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE
);

-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT,
    description TEXT,
    model_type TEXT NOT NULL CHECK (
        model_type IN ('chat', 'embedding', 'completion', 'multimodal')
    ),
    provider TEXT NOT NULL,
    model_size TEXT,
    quantization TEXT,
    file_path TEXT,
    download_url TEXT,
    config TEXT, -- JSON格式的模型配置
    status TEXT DEFAULT 'available' CHECK (
        status IN ('available', 'downloading', 'loaded', 'error')
    ),
    download_progress REAL DEFAULT 0.0,
    memory_usage INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME,
    usage_count INTEGER DEFAULT 0
);

-- 插件表
CREATE TABLE plugins (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT,
    description TEXT,
    version TEXT NOT NULL,
    author TEXT,
    homepage_url TEXT,
    download_url TEXT,
    file_path TEXT,
    config_schema TEXT, -- JSON Schema
    user_config TEXT, -- JSON格式的用户配置
    permissions TEXT, -- JSON数组
    status TEXT DEFAULT 'installed' CHECK (
        status IN ('installed', 'enabled', 'disabled', 'error')
    ),
    install_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 0
);

-- 系统配置表
CREATE TABLE system_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    value_type TEXT DEFAULT 'string' CHECK (
        value_type IN ('string', 'number', 'boolean', 'json')
    ),
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 系统日志表
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error')),
    message TEXT NOT NULL,
    module TEXT,
    function TEXT,
    line_number INTEGER,
    user_id TEXT,
    session_id TEXT,
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (session_id) REFERENCES sessions(id)
);

-- 性能指标表
CREATE TABLE performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    metric_unit TEXT,
    tags TEXT, -- JSON格式的标签
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metrics_name_time (metric_name, timestamp)
);

-- 创建索引
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_updated_at ON sessions(updated_at DESC);
CREATE INDEX idx_sessions_model ON sessions(model);

CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_messages_role ON messages(role);

CREATE INDEX idx_documents_kb_id ON documents(knowledge_base_id);
CREATE INDEX idx_documents_status ON documents(processing_status);
CREATE INDEX idx_documents_created_at ON documents(created_at DESC);

CREATE INDEX idx_models_type ON models(model_type);
CREATE INDEX idx_models_status ON models(status);
CREATE INDEX idx_models_last_used ON models(last_used_at DESC);

CREATE INDEX idx_plugins_status ON plugins(status);
CREATE INDEX idx_plugins_install_date ON plugins(install_date DESC);

CREATE INDEX idx_logs_level ON system_logs(level);
CREATE INDEX idx_logs_created_at ON system_logs(created_at DESC);
CREATE INDEX idx_logs_module ON system_logs(module);

-- 全文搜索索引
CREATE VIRTUAL TABLE messages_fts USING fts5(
    content,
    content='messages',
    content_rowid='rowid'
);

-- 触发器：自动更新FTS索引
CREATE TRIGGER messages_fts_insert AFTER INSERT ON messages BEGIN
    INSERT INTO messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;

CREATE TRIGGER messages_fts_delete AFTER DELETE ON messages BEGIN
    INSERT INTO messages_fts(messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
END;

CREATE TRIGGER messages_fts_update AFTER UPDATE ON messages BEGIN
    INSERT INTO messages_fts(messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
    INSERT INTO messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;
```

### 5.2 ChromaDB向量数据库

#### 5.2.1 向量数据库架构

ChromaDB 作为 AI Studio 的向量数据库，专门用于存储和检索文档的向量表示，支持高效的语义搜索和相似度计算。

**ChromaDB架构图：**
```
ChromaDB向量数据库架构：

┌─────────────────────────────────────────────────────────────┐
│                        集合管理层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 知识库集合   │ │ 用户文档    │ │ 模型向量    │ │ 缓存集合 │ │
│  │KnowledgeCol │ │ UserDocs    │ │ModelVectors │ │CacheCol │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        向量存储层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   向量数据   │ │   元数据    │ │    索引     │ │ 距离计算 │ │
│  │  Vectors    │ │  Metadata   │ │   Index     │ │Distance │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        检索优化层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   HNSW索引   │ │   过滤器    │ │   重排序    │ │ 结果聚合 │ │
│  │HNSW Index   │ │  Filters    │ │  Rerank     │ │Aggregate│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘

向量检索流程：
查询向量 → 索引搜索 → 候选筛选 → 距离计算 → 结果排序 → 元数据填充

详细检索过程：
┌─────────────────────────────────────────────────────────────┐
│                    查询预处理                                │
│ 1. 查询向量化 → 2. 维度检查 → 3. 归一化处理                 │
│ 4. 过滤条件解析 → 5. 搜索参数验证                           │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    索引搜索                                  │
│                                                             │
│ [HNSW搜索] → 入口点选择 → 层级遍历 → 邻居扩展               │
│     ↓                                                       │
│ 候选队列维护 → 距离计算 → 剪枝优化 → 结果收集               │
│                                                             │
│ [暴力搜索] → 全量扫描 → 距离计算 → TopK选择                 │
│     ↓                                                       │
│ 并行计算 → 内存优化 → 早停策略 → 精确结果                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    结果后处理                                │
│ • 元数据关联 • 相似度转换 • 结果去重 • 分页处理             │
│ • 高亮标记 • 上下文扩展 • 质量评分 • 缓存更新               │
└─────────────────────────────────────────────────────────────┘
```

**ChromaDB集成实现**
```rust
// src/database/chroma.rs
use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use reqwest::Client;
use tokio::sync::RwLock;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChromaDocument {
    pub id: String,
    pub content: String,
    pub metadata: HashMap<String, serde_json::Value>,
    pub embedding: Option<Vec<f32>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchQuery {
    pub query_embeddings: Vec<Vec<f32>>,
    pub n_results: usize,
    pub where_clause: Option<HashMap<String, serde_json::Value>>,
    pub where_document: Option<HashMap<String, String>>,
    pub include: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub ids: Vec<Vec<String>>,
    pub distances: Vec<Vec<f32>>,
    pub metadatas: Vec<Vec<Option<HashMap<String, serde_json::Value>>>>,
    pub documents: Vec<Vec<Option<String>>>,
    pub embeddings: Option<Vec<Vec<Vec<f32>>>>,
}

pub struct ChromaClient {
    client: Client,
    base_url: String,
    collections: Arc<RwLock<HashMap<String, ChromaCollection>>>,
}

#[derive(Debug, Clone)]
pub struct ChromaCollection {
    pub name: String,
    pub embedding_function: String,
    pub metadata: HashMap<String, serde_json::Value>,
    pub dimension: usize,
    pub document_count: usize,
}

impl ChromaClient {
    pub fn new(base_url: &str) -> Self {
        Self {
            client: Client::new(),
            base_url: base_url.to_string(),
            collections: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    // 创建集合
    pub async fn create_collection(
        &self,
        name: &str,
        embedding_function: &str,
        metadata: Option<HashMap<String, serde_json::Value>>,
    ) -> Result<ChromaCollection, ChromaError> {
        let request_body = serde_json::json!({
            "name": name,
            "metadata": metadata.unwrap_or_default(),
            "get_or_create": true
        });

        let response = self.client
            .post(&format!("{}/api/v1/collections", self.base_url))
            .json(&request_body)
            .send()
            .await?;

        if response.status().is_success() {
            let collection = ChromaCollection {
                name: name.to_string(),
                embedding_function: embedding_function.to_string(),
                metadata: metadata.unwrap_or_default(),
                dimension: 0, // 将在第一次添加文档时确定
                document_count: 0,
            };

            let mut collections = self.collections.write().await;
            collections.insert(name.to_string(), collection.clone());

            Ok(collection)
        } else {
            Err(ChromaError::ApiError(response.status().to_string()))
        }
    }

    // 添加文档
    pub async fn add_documents(
        &self,
        collection_name: &str,
        documents: Vec<ChromaDocument>,
    ) -> Result<(), ChromaError> {
        let ids: Vec<String> = documents.iter().map(|d| d.id.clone()).collect();
        let contents: Vec<String> = documents.iter().map(|d| d.content.clone()).collect();
        let metadatas: Vec<HashMap<String, serde_json::Value>> =
            documents.iter().map(|d| d.metadata.clone()).collect();
        let embeddings: Option<Vec<Vec<f32>>> = if documents.iter().any(|d| d.embedding.is_some()) {
            Some(documents.iter().map(|d| d.embedding.clone().unwrap_or_default()).collect())
        } else {
            None
        };

        let request_body = serde_json::json!({
            "ids": ids,
            "documents": contents,
            "metadatas": metadatas,
            "embeddings": embeddings
        });

        let response = self.client
            .post(&format!("{}/api/v1/collections/{}/add", self.base_url, collection_name))
            .json(&request_body)
            .send()
            .await?;

        if response.status().is_success() {
            // 更新集合统计
            let mut collections = self.collections.write().await;
            if let Some(collection) = collections.get_mut(collection_name) {
                collection.document_count += documents.len();
                if collection.dimension == 0 && !documents.is_empty() {
                    if let Some(ref embedding) = documents[0].embedding {
                        collection.dimension = embedding.len();
                    }
                }
            }
            Ok(())
        } else {
            Err(ChromaError::ApiError(response.status().to_string()))
        }
    }

    // 搜索文档
    pub async fn search(
        &self,
        collection_name: &str,
        query: SearchQuery,
    ) -> Result<SearchResult, ChromaError> {
        let response = self.client
            .post(&format!("{}/api/v1/collections/{}/query", self.base_url, collection_name))
            .json(&query)
            .send()
            .await?;

        if response.status().is_success() {
            let result: SearchResult = response.json().await?;
            Ok(result)
        } else {
            Err(ChromaError::ApiError(response.status().to_string()))
        }
    }

    // 删除文档
    pub async fn delete_documents(
        &self,
        collection_name: &str,
        ids: Vec<String>,
    ) -> Result<(), ChromaError> {
        let request_body = serde_json::json!({
            "ids": ids
        });

        let response = self.client
            .post(&format!("{}/api/v1/collections/{}/delete", self.base_url, collection_name))
            .json(&request_body)
            .send()
            .await?;

        if response.status().is_success() {
            // 更新集合统计
            let mut collections = self.collections.write().await;
            if let Some(collection) = collections.get_mut(collection_name) {
                collection.document_count = collection.document_count.saturating_sub(ids.len());
            }
            Ok(())
        } else {
            Err(ChromaError::ApiError(response.status().to_string()))
        }
    }

    // 获取集合信息
    pub async fn get_collection(&self, name: &str) -> Result<ChromaCollection, ChromaError> {
        let collections = self.collections.read().await;
        if let Some(collection) = collections.get(name) {
            Ok(collection.clone())
        } else {
            Err(ChromaError::CollectionNotFound(name.to_string()))
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ChromaError {
    #[error("HTTP request failed: {0}")]
    RequestError(#[from] reqwest::Error),

    #[error("API error: {0}")]
    ApiError(String),

    #[error("Collection not found: {0}")]
    CollectionNotFound(String),

    #[error("Invalid embedding dimension: expected {expected}, got {actual}")]
    DimensionMismatch { expected: usize, actual: usize },

    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
}
```

---

## 第六部分：用户界面设计

### 6.1 组件库设计规范

#### 6.1.1 设计系统架构

AI Studio 采用原子设计方法论构建组件库，确保界面的一致性、可维护性和可扩展性。

**设计系统层次结构：**
```
设计系统架构：

┌─────────────────────────────────────────────────────────────┐
│                        设计令牌层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │    颜色     │ │    字体     │ │    间距     │ │   动画   │ │
│  │   Colors    │ │Typography   │ │  Spacing    │ │Animation│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        原子组件层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │    按钮     │ │    输入框    │ │    图标     │ │   标签   │ │
│  │   Button    │ │    Input    │ │    Icon     │ │  Label  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        分子组件层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   搜索框    │ │   下拉菜单   │ │   卡片组件   │ │ 表单组件 │ │
│  │SearchField  │ │  Dropdown   │ │    Card     │ │FormField│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        有机体组件层                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │    导航栏    │ │   侧边栏    │ │   数据表格   │ │ 模态框   │ │
│  │  Navigation │ │   Sidebar   │ │ DataTable   │ │  Modal  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        模板层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   页面布局   │ │   表单布局   │ │   列表布局   │ │ 详情布局 │ │
│  │PageTemplate │ │FormTemplate │ │ListTemplate │ │DetailTpl│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**设计令牌定义**
```typescript
// src/design-system/tokens.ts
export const designTokens = {
  // 颜色系统
  colors: {
    // 主色调
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9', // 主色
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
    },

    // 中性色
    neutral: {
      0: '#ffffff',
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      1000: '#000000',
    },

    // 语义色
    semantic: {
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },

    // 功能色
    functional: {
      background: {
        primary: 'var(--color-bg-primary)',
        secondary: 'var(--color-bg-secondary)',
        tertiary: 'var(--color-bg-tertiary)',
      },
      text: {
        primary: 'var(--color-text-primary)',
        secondary: 'var(--color-text-secondary)',
        tertiary: 'var(--color-text-tertiary)',
        inverse: 'var(--color-text-inverse)',
      },
      border: {
        primary: 'var(--color-border-primary)',
        secondary: 'var(--color-border-secondary)',
        focus: 'var(--color-border-focus)',
      },
    },
  },

  // 字体系统
  typography: {
    fontFamily: {
      sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },

  // 间距系统
  spacing: {
    0: '0',
    1: '0.25rem',   // 4px
    2: '0.5rem',    // 8px
    3: '0.75rem',   // 12px
    4: '1rem',      // 16px
    5: '1.25rem',   // 20px
    6: '1.5rem',    // 24px
    8: '2rem',      // 32px
    10: '2.5rem',   // 40px
    12: '3rem',     // 48px
    16: '4rem',     // 64px
    20: '5rem',     // 80px
    24: '6rem',     // 96px
  },

  // 圆角系统
  borderRadius: {
    none: '0',
    sm: '0.25rem',   // 4px
    base: '0.375rem', // 6px
    md: '0.5rem',    // 8px
    lg: '0.75rem',   // 12px
    xl: '1rem',      // 16px
    '2xl': '1.5rem', // 24px
    full: '9999px',
  },

  // 阴影系统
  boxShadow: {
    none: 'none',
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },

  // 动画系统
  animation: {
    duration: {
      fast: '150ms',
      normal: '250ms',
      slow: '350ms',
    },
    easing: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },

  // Z-index系统
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },
} as const;

// 类型定义
export type DesignTokens = typeof designTokens;
export type ColorScale = keyof typeof designTokens.colors.primary;
export type FontSize = keyof typeof designTokens.typography.fontSize;
export type Spacing = keyof typeof designTokens.spacing;
```

#### 6.1.2 核心组件实现

**Button组件实现**
```vue
<!-- src/components/common/Button.vue -->
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="type"
    @click="handleClick"
  >
    <Icon
      v-if="loading"
      name="spinner"
      class="animate-spin"
      :size="iconSize"
    />
    <Icon
      v-else-if="icon && iconPosition === 'left'"
      :name="icon"
      :size="iconSize"
    />

    <span v-if="$slots.default" :class="textClasses">
      <slot />
    </span>

    <Icon
      v-if="icon && iconPosition === 'right' && !loading"
      :name="icon"
      :size="iconSize"
    />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from './Icon.vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  icon?: string
  iconPosition?: 'left' | 'right'
  type?: 'button' | 'submit' | 'reset'
  fullWidth?: boolean
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  iconPosition: 'left',
  type: 'button',
  fullWidth: false,
})

const emit = defineEmits<Emits>()

const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex',
    'items-center',
    'justify-center',
    'font-medium',
    'rounded-md',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
  ]

  // 尺寸样式
  const sizeClasses = {
    xs: ['px-2', 'py-1', 'text-xs'],
    sm: ['px-3', 'py-1.5', 'text-sm'],
    md: ['px-4', 'py-2', 'text-sm'],
    lg: ['px-6', 'py-3', 'text-base'],
    xl: ['px-8', 'py-4', 'text-lg'],
  }

  // 变体样式
  const variantClasses = {
    primary: [
      'bg-primary-500',
      'text-white',
      'hover:bg-primary-600',
      'focus:ring-primary-500',
      'active:bg-primary-700',
    ],
    secondary: [
      'bg-neutral-100',
      'text-neutral-900',
      'hover:bg-neutral-200',
      'focus:ring-neutral-500',
      'active:bg-neutral-300',
      'dark:bg-neutral-800',
      'dark:text-neutral-100',
      'dark:hover:bg-neutral-700',
    ],
    outline: [
      'border',
      'border-neutral-300',
      'text-neutral-700',
      'hover:bg-neutral-50',
      'focus:ring-primary-500',
      'active:bg-neutral-100',
      'dark:border-neutral-600',
      'dark:text-neutral-300',
      'dark:hover:bg-neutral-800',
    ],
    ghost: [
      'text-neutral-700',
      'hover:bg-neutral-100',
      'focus:ring-primary-500',
      'active:bg-neutral-200',
      'dark:text-neutral-300',
      'dark:hover:bg-neutral-800',
    ],
    danger: [
      'bg-red-500',
      'text-white',
      'hover:bg-red-600',
      'focus:ring-red-500',
      'active:bg-red-700',
    ],
  }

  const widthClasses = props.fullWidth ? ['w-full'] : []

  return [
    ...baseClasses,
    ...sizeClasses[props.size],
    ...variantClasses[props.variant],
    ...widthClasses,
  ]
})

const textClasses = computed(() => {
  const hasIcon = props.icon && !props.loading
  const spacing = {
    xs: hasIcon ? (props.iconPosition === 'left' ? 'ml-1' : 'mr-1') : '',
    sm: hasIcon ? (props.iconPosition === 'left' ? 'ml-1.5' : 'mr-1.5') : '',
    md: hasIcon ? (props.iconPosition === 'left' ? 'ml-2' : 'mr-2') : '',
    lg: hasIcon ? (props.iconPosition === 'left' ? 'ml-2' : 'mr-2') : '',
    xl: hasIcon ? (props.iconPosition === 'left' ? 'ml-3' : 'mr-3') : '',
  }

  return spacing[props.size]
})

const iconSize = computed(() => {
  const sizes = {
    xs: 'sm',
    sm: 'sm',
    md: 'md',
    lg: 'md',
    xl: 'lg',
  }
  return sizes[props.size]
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
```

### 6.2 **主题系统增强**

#### 6.2.1 深色/浅色切换架构

```
主题切换系统架构：

用户操作 → 主题检测 → 状态更新 → 样式应用 → 持久化存储

主题切换流程：
┌─────────────────────────────────────────────────────────────┐
│                    主题检测层                                │
│ 1. 系统主题检测 → 2. 用户偏好读取 → 3. 默认主题设置         │
│ 4. 媒体查询监听 → 5. 存储同步 → 6. 初始化完成               │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    主题管理层                                │
│                                                             │
│ [主题状态] → 当前主题 → 可用主题 → 切换历史                 │
│     ↓                                                       │
│ 状态管理 → 响应式更新 → 组件通知 → 事件发布                │
│                                                             │
│ [主题应用] → CSS变量更新 → 类名切换 → 动画过渡              │
│     ↓                                                       │
│ DOM操作 → 样式计算 → 重绘优化 → 性能监控                   │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    持久化层                                  │
│ • 本地存储 • 用户偏好同步 • 跨设备一致性 • 备份恢复         │
└─────────────────────────────────────────────────────────────┘
```

**主题管理实现**
```typescript
// src/stores/theme.ts
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'system'
export type ThemeVariant = 'default' | 'high-contrast' | 'colorful'

interface ThemeConfig {
  mode: ThemeMode
  variant: ThemeVariant
  customColors?: Record<string, string>
  animations: boolean
  reducedMotion: boolean
}

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const config = ref<ThemeConfig>({
    mode: 'system',
    variant: 'default',
    animations: true,
    reducedMotion: false,
  })

  const systemTheme = ref<'light' | 'dark'>('light')
  const isInitialized = ref(false)

  // 计算属性
  const currentTheme = computed(() => {
    if (config.value.mode === 'system') {
      return systemTheme.value
    }
    return config.value.mode
  })

  const themeClasses = computed(() => {
    const classes = [`theme-${currentTheme.value}`]

    if (config.value.variant !== 'default') {
      classes.push(`variant-${config.value.variant}`)
    }

    if (!config.value.animations) {
      classes.push('no-animations')
    }

    if (config.value.reducedMotion) {
      classes.push('reduced-motion')
    }

    return classes
  })

  const isDark = computed(() => currentTheme.value === 'dark')

  // 方法
  const initializeTheme = async () => {
    try {
      // 读取存储的配置
      const stored = localStorage.getItem('theme-config')
      if (stored) {
        const parsedConfig = JSON.parse(stored)
        Object.assign(config.value, parsedConfig)
      }

      // 检测系统主题
      detectSystemTheme()

      // 监听系统主题变化
      setupSystemThemeListener()

      // 应用主题
      applyTheme()

      // 检测用户偏好
      detectUserPreferences()

      isInitialized.value = true
    } catch (error) {
      console.error('Failed to initialize theme:', error)
      // 使用默认配置
      applyTheme()
      isInitialized.value = true
    }
  }

  const setThemeMode = (mode: ThemeMode) => {
    config.value.mode = mode
    applyTheme()
    persistConfig()
  }

  const setThemeVariant = (variant: ThemeVariant) => {
    config.value.variant = variant
    applyTheme()
    persistConfig()
  }

  const toggleTheme = () => {
    if (config.value.mode === 'system') {
      setThemeMode(systemTheme.value === 'light' ? 'dark' : 'light')
    } else {
      setThemeMode(config.value.mode === 'light' ? 'dark' : 'light')
    }
  }

  const setCustomColor = (key: string, value: string) => {
    if (!config.value.customColors) {
      config.value.customColors = {}
    }
    config.value.customColors[key] = value
    applyCustomColors()
    persistConfig()
  }

  const resetTheme = () => {
    config.value = {
      mode: 'system',
      variant: 'default',
      animations: true,
      reducedMotion: false,
    }
    applyTheme()
    persistConfig()
  }

  // 内部方法
  const detectSystemTheme = () => {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      systemTheme.value = mediaQuery.matches ? 'dark' : 'light'
    }
  }

  const setupSystemThemeListener = () => {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', (e) => {
        systemTheme.value = e.matches ? 'dark' : 'light'
        if (config.value.mode === 'system') {
          applyTheme()
        }
      })
    }
  }

  const detectUserPreferences = () => {
    if (typeof window !== 'undefined') {
      // 检测用户是否偏好减少动画
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)')
      config.value.reducedMotion = prefersReducedMotion.matches

      // 监听变化
      prefersReducedMotion.addEventListener('change', (e) => {
        config.value.reducedMotion = e.matches
        applyTheme()
        persistConfig()
      })
    }
  }

  const applyTheme = () => {
    if (typeof document === 'undefined') return

    const root = document.documentElement

    // 移除旧的主题类
    root.classList.remove('theme-light', 'theme-dark', 'variant-default', 'variant-high-contrast', 'variant-colorful')

    // 应用新的主题类
    themeClasses.value.forEach(className => {
      root.classList.add(className)
    })

    // 应用自定义颜色
    applyCustomColors()

    // 触发主题变更事件
    document.dispatchEvent(new CustomEvent('theme-changed', {
      detail: {
        theme: currentTheme.value,
        variant: config.value.variant,
        config: config.value,
      }
    }))
  }

  const applyCustomColors = () => {
    if (!config.value.customColors || typeof document === 'undefined') return

    const root = document.documentElement
    Object.entries(config.value.customColors).forEach(([key, value]) => {
      root.style.setProperty(`--color-custom-${key}`, value)
    })
  }

  const persistConfig = () => {
    try {
      localStorage.setItem('theme-config', JSON.stringify(config.value))
    } catch (error) {
      console.error('Failed to persist theme config:', error)
    }
  }

  // 监听配置变化
  watch(
    () => config.value,
    () => {
      if (isInitialized.value) {
        applyTheme()
      }
    },
    { deep: true }
  )

  return {
    // 状态
    config,
    currentTheme,
    themeClasses,
    isDark,
    isInitialized,

    // 方法
    initializeTheme,
    setThemeMode,
    setThemeVariant,
    toggleTheme,
    setCustomColor,
    resetTheme,
  }
})
```

### 6.3 **国际化方案增强**

#### 6.3.1 中英文切换流程

```
国际化系统架构：

语言检测 → 资源加载 → 内容渲染 → 格式化处理 → 缓存管理

多语言支持流程：
┌─────────────────────────────────────────────────────────────┐
│                    语言检测层                                │
│ 1. 浏览器语言检测 → 2. 用户偏好读取 → 3. 默认语言设置       │
│ 4. 区域设置检测 → 5. 回退机制 → 6. 语言验证                 │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    资源管理层                                │
│                                                             │
│ [语言包加载] → 动态导入 → 懒加载 → 缓存策略                 │
│     ↓                                                       │
│ 资源合并 → 命名空间 → 版本控制 → 热更新                    │
│                                                             │
│ [翻译引擎] → 键值映射 → 插值处理 → 复数规则                 │
│     ↓                                                       │
│ 上下文处理 → 性别变化 → 格式化 → 验证检查                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    格式化层                                  │
│ • 日期时间格式化 • 数字货币格式化 • 相对时间 • 单位转换     │
│ • 排序规则 • 文本方向 • 字体选择 • 布局调整                 │
└─────────────────────────────────────────────────────────────┘
```

**国际化实现**
```typescript
// src/stores/i18n.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createI18n } from 'vue-i18n'

export type SupportedLocale = 'zh-CN' | 'en-US'
export type MessageKey = string
export type MessageValue = string | Record<string, any>

interface LocaleConfig {
  code: SupportedLocale
  name: string
  nativeName: string
  flag: string
  rtl: boolean
  dateFormat: string
  timeFormat: string
  numberFormat: Intl.NumberFormatOptions
}

const localeConfigs: Record<SupportedLocale, LocaleConfig> = {
  'zh-CN': {
    code: 'zh-CN',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false,
    dateFormat: 'YYYY年MM月DD日',
    timeFormat: 'HH:mm:ss',
    numberFormat: {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    },
  },
  'en-US': {
    code: 'en-US',
    name: 'English (United States)',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
    dateFormat: 'MM/DD/YYYY',
    timeFormat: 'h:mm:ss A',
    numberFormat: {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    },
  },
}

export const useI18nStore = defineStore('i18n', () => {
  // 状态
  const currentLocale = ref<SupportedLocale>('zh-CN')
  const loadedLocales = ref<Set<SupportedLocale>>(new Set())
  const messages = ref<Record<SupportedLocale, Record<string, any>>>({
    'zh-CN': {},
    'en-US': {},
  })
  const isLoading = ref(false)

  // 计算属性
  const localeConfig = computed(() => localeConfigs[currentLocale.value])
  const availableLocales = computed(() => Object.values(localeConfigs))
  const isRTL = computed(() => localeConfig.value.rtl)

  // 方法
  const initializeI18n = async () => {
    try {
      // 检测用户偏好语言
      const preferredLocale = detectPreferredLocale()

      // 加载默认语言包
      await loadLocaleMessages(preferredLocale)

      // 设置当前语言
      await setLocale(preferredLocale)

      return true
    } catch (error) {
      console.error('Failed to initialize i18n:', error)
      return false
    }
  }

  const setLocale = async (locale: SupportedLocale) => {
    try {
      // 加载语言包（如果未加载）
      if (!loadedLocales.value.has(locale)) {
        await loadLocaleMessages(locale)
      }

      // 更新当前语言
      currentLocale.value = locale

      // 更新HTML lang属性
      if (typeof document !== 'undefined') {
        document.documentElement.lang = locale
        document.documentElement.dir = localeConfigs[locale].rtl ? 'rtl' : 'ltr'
      }

      // 持久化设置
      persistLocale(locale)

      // 触发语言变更事件
      if (typeof document !== 'undefined') {
        document.dispatchEvent(new CustomEvent('locale-changed', {
          detail: { locale, config: localeConfigs[locale] }
        }))
      }

      return true
    } catch (error) {
      console.error('Failed to set locale:', error)
      return false
    }
  }

  const loadLocaleMessages = async (locale: SupportedLocale) => {
    if (loadedLocales.value.has(locale)) {
      return messages.value[locale]
    }

    isLoading.value = true

    try {
      // 动态导入语言包
      const localeMessages = await import(`../locales/${locale}.json`)

      // 合并消息
      messages.value[locale] = {
        ...messages.value[locale],
        ...localeMessages.default,
      }

      loadedLocales.value.add(locale)

      return messages.value[locale]
    } catch (error) {
      console.error(`Failed to load locale messages for ${locale}:`, error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const t = (key: MessageKey, params?: Record<string, any>): string => {
    const message = getNestedMessage(messages.value[currentLocale.value], key)

    if (typeof message === 'string') {
      return interpolateMessage(message, params)
    }

    // 回退到英文
    if (currentLocale.value !== 'en-US') {
      const fallbackMessage = getNestedMessage(messages.value['en-US'], key)
      if (typeof fallbackMessage === 'string') {
        return interpolateMessage(fallbackMessage, params)
      }
    }

    // 返回键名作为最后的回退
    return key
  }

  const formatDate = (date: Date | string | number, format?: string): string => {
    const dateObj = new Date(date)
    const formatStr = format || localeConfig.value.dateFormat

    return new Intl.DateTimeFormat(currentLocale.value, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }).format(dateObj)
  }

  const formatTime = (date: Date | string | number, format?: string): string => {
    const dateObj = new Date(date)

    return new Intl.DateTimeFormat(currentLocale.value, {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: currentLocale.value === 'en-US',
    }).format(dateObj)
  }

  const formatNumber = (
    number: number,
    options?: Intl.NumberFormatOptions
  ): string => {
    const formatOptions = {
      ...localeConfig.value.numberFormat,
      ...options,
    }

    return new Intl.NumberFormat(currentLocale.value, formatOptions).format(number)
  }

  const formatCurrency = (
    amount: number,
    currency: string = 'USD'
  ): string => {
    return new Intl.NumberFormat(currentLocale.value, {
      style: 'currency',
      currency,
    }).format(amount)
  }

  const formatRelativeTime = (date: Date | string | number): string => {
    const now = new Date()
    const targetDate = new Date(date)
    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

    const rtf = new Intl.RelativeTimeFormat(currentLocale.value, { numeric: 'auto' })

    if (Math.abs(diffInSeconds) < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (Math.abs(diffInSeconds) < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (Math.abs(diffInSeconds) < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    }
  }

  // 内部方法
  const detectPreferredLocale = (): SupportedLocale => {
    // 1. 检查存储的偏好
    const stored = localStorage.getItem('preferred-locale')
    if (stored && Object.keys(localeConfigs).includes(stored)) {
      return stored as SupportedLocale
    }

    // 2. 检查浏览器语言
    if (typeof navigator !== 'undefined') {
      const browserLang = navigator.language
      if (browserLang.startsWith('zh')) {
        return 'zh-CN'
      } else if (browserLang.startsWith('en')) {
        return 'en-US'
      }
    }

    // 3. 默认语言
    return 'zh-CN'
  }

  const getNestedMessage = (messages: Record<string, any>, key: string): any => {
    return key.split('.').reduce((obj, k) => obj?.[k], messages)
  }

  const interpolateMessage = (message: string, params?: Record<string, any>): string => {
    if (!params) return message

    return message.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key]?.toString() || match
    })
  }

  const persistLocale = (locale: SupportedLocale) => {
    try {
      localStorage.setItem('preferred-locale', locale)
    } catch (error) {
      console.error('Failed to persist locale:', error)
    }
  }

  return {
    // 状态
    currentLocale,
    localeConfig,
    availableLocales,
    isRTL,
    isLoading,

    // 方法
    initializeI18n,
    setLocale,
    t,
    formatDate,
    formatTime,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
  }
})
```

### 6.4 **用户系统设计**

#### 6.4.1 游客/登录态转换图

```
用户系统状态转换：

游客模式 ⇄ 注册流程 ⇄ 登录状态 ⇄ 账户管理

用户状态转换流程：
┌─────────────────────────────────────────────────────────────┐
│                    游客模式 (Guest)                          │
│ • 基础功能使用 • 本地数据存储 • 功能限制提示                 │
│ • 数据临时性 • 无云端同步 • 隐私保护                       │
│                                                             │
│ [转换触发器]                                                │
│ • 点击注册按钮 • 数据同步需求 • 高级功能访问                │
│ • 数据备份需求 • 跨设备使用 • 协作功能                     │
└─────────────────────────────────────────────────────────────┘
        ↓ 注册流程
┌─────────────────────────────────────────────────────────────┐
│                    注册流程 (Registration)                   │
│                                                             │
│ [信息收集] → 用户名 → 邮箱 → 密码 → 确认密码                │
│     ↓                                                       │
│ 验证检查 → 格式验证 → 唯一性检查 → 强度验证                │
│     ↓                                                       │
│ [邮箱验证] → 发送验证码 → 用户确认 → 账户激活               │
│     ↓                                                       │
│ 数据迁移 → 本地数据 → 云端同步 → 设置保留                  │
│     ↓                                                       │
│ 欢迎引导 → 功能介绍 → 偏好设置 → 完成注册                  │
└─────────────────────────────────────────────────────────────┘
        ↓ 自动登录
┌─────────────────────────────────────────────────────────────┐
│                    登录状态 (Authenticated)                  │
│ • 完整功能访问 • 云端数据同步 • 个性化设置                 │
│ • 协作功能 • 数据备份 • 跨设备同步                         │
│                                                             │
│ [会话管理]                                                  │
│ • 令牌刷新 • 自动续期 • 安全检查 • 异常检测                │
│                                                             │
│ [状态维护]                                                  │
│ • 心跳检测 • 网络重连 • 数据同步 • 冲突解决                │
└─────────────────────────────────────────────────────────────┘
        ↓ 账户操作
┌─────────────────────────────────────────────────────────────┐
│                    账户管理 (Account Management)             │
│                                                             │
│ [个人信息] → 头像上传 → 昵称修改 → 邮箱更换                 │
│     ↓                                                       │
│ 验证流程 → 身份确认 → 安全检查 → 更新确认                  │
│                                                             │
│ [安全设置] → 密码修改 → 两步验证 → 登录历史                 │
│     ↓                                                       │
│ 设备管理 → 会话管理 → 权限控制 → 安全审计                  │
│                                                             │
│ [数据管理] → 导出数据 → 删除数据 → 账户注销                 │
│     ↓                                                       │
│ 确认流程 → 数据清理 → 通知发送 → 操作完成                  │
└─────────────────────────────────────────────────────────────┘

权限分级控制表：
┌─────────────────────────────────────────────────────────────┐
│ 功能模块 │ 游客模式 │ 注册用户 │ 高级用户 │ 管理员用户 │
├─────────────────────────────────────────────────────────────┤
│ 基础聊天 │    ✓     │    ✓     │    ✓     │     ✓      │
│ 会话保存 │    ✗     │    ✓     │    ✓     │     ✓      │
│ 云端同步 │    ✗     │    ✓     │    ✓     │     ✓      │
│ 知识库   │   限制    │    ✓     │    ✓     │     ✓      │
│ 模型下载 │   限制    │   限制    │    ✓     │     ✓      │
│ 插件安装 │    ✗     │   限制    │    ✓     │     ✓      │
│ 网络共享 │    ✗     │    ✗     │    ✓     │     ✓      │
│ 系统设置 │   限制    │   限制    │   限制    │     ✓      │
│ 用户管理 │    ✗     │    ✗     │    ✗     │     ✓      │
│ 系统监控 │    ✗     │    ✗     │    ✗     │     ✓      │
└─────────────────────────────────────────────────────────────┘
```

**用户认证实现**
```typescript
// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { invoke } from '@tauri-apps/api/tauri'

export type UserRole = 'guest' | 'user' | 'premium' | 'admin'
export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated' | 'error'

interface User {
  id: string
  username: string
  email: string
  displayName: string
  avatar?: string
  role: UserRole
  createdAt: string
  lastLoginAt: string
  preferences: UserPreferences
}

interface UserPreferences {
  theme: string
  language: string
  notifications: boolean
  autoSave: boolean
  dataSync: boolean
}

interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

interface RegisterData {
  username: string
  email: string
  password: string
  displayName: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const status = ref<AuthStatus>('loading')
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const sessionExpiry = ref<number | null>(null)
  const isGuestMode = ref(true)

  // 计算属性
  const isAuthenticated = computed(() => status.value === 'authenticated' && !!user.value)
  const isGuest = computed(() => isGuestMode.value || !isAuthenticated.value)
  const userRole = computed(() => user.value?.role || 'guest')
  const canAccess = computed(() => (requiredRole: UserRole) => {
    const roleHierarchy: Record<UserRole, number> = {
      guest: 0,
      user: 1,
      premium: 2,
      admin: 3,
    }

    const currentLevel = roleHierarchy[userRole.value]
    const requiredLevel = roleHierarchy[requiredRole]

    return currentLevel >= requiredLevel
  })

  // 方法
  const initializeAuth = async () => {
    try {
      status.value = 'loading'

      // 检查存储的令牌
      const storedToken = localStorage.getItem('auth-token')
      const storedRefreshToken = localStorage.getItem('refresh-token')

      if (storedToken && storedRefreshToken) {
        token.value = storedToken
        refreshToken.value = storedRefreshToken

        // 验证令牌
        const isValid = await validateToken(storedToken)
        if (isValid) {
          await fetchUserProfile()
          status.value = 'authenticated'
          isGuestMode.value = false
          return true
        } else {
          // 尝试刷新令牌
          const refreshed = await refreshAuthToken()
          if (refreshed) {
            await fetchUserProfile()
            status.value = 'authenticated'
            isGuestMode.value = false
            return true
          }
        }
      }

      // 进入游客模式
      enterGuestMode()
      return false
    } catch (error) {
      console.error('Failed to initialize auth:', error)
      enterGuestMode()
      return false
    }
  }

  const login = async (credentials: LoginCredentials) => {
    try {
      status.value = 'loading'

      const response = await invoke('auth_login', { credentials })

      if (response.success) {
        token.value = response.token
        refreshToken.value = response.refreshToken
        sessionExpiry.value = response.expiresAt

        // 存储令牌
        if (credentials.rememberMe) {
          localStorage.setItem('auth-token', response.token)
          localStorage.setItem('refresh-token', response.refreshToken)
        } else {
          sessionStorage.setItem('auth-token', response.token)
          sessionStorage.setItem('refresh-token', response.refreshToken)
        }

        // 获取用户信息
        await fetchUserProfile()

        // 迁移游客数据
        await migrateGuestData()

        status.value = 'authenticated'
        isGuestMode.value = false

        return { success: true }
      } else {
        status.value = 'unauthenticated'
        return { success: false, error: response.error }
      }
    } catch (error) {
      status.value = 'error'
      return { success: false, error: error.message }
    }
  }

  const register = async (data: RegisterData) => {
    try {
      status.value = 'loading'

      const response = await invoke('auth_register', { data })

      if (response.success) {
        // 注册成功，等待邮箱验证
        return {
          success: true,
          message: 'Registration successful. Please check your email for verification.'
        }
      } else {
        status.value = 'unauthenticated'
        return { success: false, error: response.error }
      }
    } catch (error) {
      status.value = 'error'
      return { success: false, error: error.message }
    }
  }

  const logout = async () => {
    try {
      // 调用后端登出
      if (token.value) {
        await invoke('auth_logout', { token: token.value })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清理状态
      user.value = null
      token.value = null
      refreshToken.value = null
      sessionExpiry.value = null
      status.value = 'unauthenticated'

      // 清理存储
      localStorage.removeItem('auth-token')
      localStorage.removeItem('refresh-token')
      sessionStorage.removeItem('auth-token')
      sessionStorage.removeItem('refresh-token')

      // 进入游客模式
      enterGuestMode()
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    try {
      const response = await invoke('auth_update_profile', {
        token: token.value,
        updates
      })

      if (response.success && user.value) {
        Object.assign(user.value, updates)
        return { success: true }
      } else {
        return { success: false, error: response.error }
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      const response = await invoke('auth_change_password', {
        token: token.value,
        currentPassword,
        newPassword,
      })

      return response
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  // 内部方法
  const enterGuestMode = () => {
    isGuestMode.value = true
    status.value = 'unauthenticated'
    user.value = {
      id: 'guest',
      username: 'Guest',
      email: '',
      displayName: 'Guest User',
      role: 'guest',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      preferences: {
        theme: 'system',
        language: 'zh-CN',
        notifications: false,
        autoSave: true,
        dataSync: false,
      },
    }
  }

  const validateToken = async (token: string): Promise<boolean> => {
    try {
      const response = await invoke('auth_validate_token', { token })
      return response.valid
    } catch (error) {
      return false
    }
  }

  const refreshAuthToken = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) return false

      const response = await invoke('auth_refresh_token', {
        refreshToken: refreshToken.value
      })

      if (response.success) {
        token.value = response.token
        sessionExpiry.value = response.expiresAt

        // 更新存储
        const storage = localStorage.getItem('auth-token') ? localStorage : sessionStorage
        storage.setItem('auth-token', response.token)

        return true
      }

      return false
    } catch (error) {
      return false
    }
  }

  const fetchUserProfile = async () => {
    try {
      const response = await invoke('auth_get_profile', { token: token.value })

      if (response.success) {
        user.value = response.user
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
    }
  }

  const migrateGuestData = async () => {
    try {
      // 迁移本地数据到用户账户
      await invoke('migrate_guest_data', {
        token: token.value,
        guestData: getGuestData()
      })
    } catch (error) {
      console.error('Failed to migrate guest data:', error)
    }
  }

  const getGuestData = () => {
    // 收集游客模式下的数据
    return {
      sessions: JSON.parse(localStorage.getItem('guest-sessions') || '[]'),
      settings: JSON.parse(localStorage.getItem('guest-settings') || '{}'),
      knowledgeBases: JSON.parse(localStorage.getItem('guest-knowledge') || '[]'),
    }
  }

  return {
    // 状态
    user,
    status,
    isAuthenticated,
    isGuest,
    userRole,
    canAccess,

    // 方法
    initializeAuth,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
  }
})
```

---

## 第七部分：系统流程设计

### 7.1 **增强操作流程图**（带状态标注）

#### 7.1.1 用户操作完整流程

```
AI Studio 用户操作完整流程图：

应用启动 → 初始化 → 用户认证 → 主界面 → 功能操作 → 数据处理 → 结果展示

详细操作流程：
┌─────────────────────────────────────────────────────────────┐
│                    应用启动阶段                              │
│ [状态: INITIALIZING]                                        │
│                                                             │
│ 1. 系统检查 → 依赖验证 → 配置加载 → 数据库连接               │
│     ↓                                                       │
│ 2. 服务启动 → AI引擎初始化 → 插件加载 → 网络检测             │
│     ↓                                                       │
│ 3. 界面渲染 → 主题应用 → 语言设置 → 状态同步                │
│     ↓                                                       │
│ [状态转换: INITIALIZING → READY]                            │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    用户认证阶段                              │
│ [状态: AUTHENTICATING]                                      │
│                                                             │
│ [游客模式路径]                                              │
│ 选择游客模式 → 本地数据初始化 → 功能限制提示                │
│     ↓                                                       │
│ [状态转换: AUTHENTICATING → GUEST_MODE]                     │
│                                                             │
│ [登录路径]                                                  │
│ 输入凭据 → 服务器验证 → 令牌获取 → 用户信息加载             │
│     ↓                                                       │
│ 数据同步 → 偏好设置 → 会话恢复 → 权限检查                  │
│     ↓                                                       │
│ [状态转换: AUTHENTICATING → AUTHENTICATED]                  │
│                                                             │
│ [注册路径]                                                  │
│ 信息填写 → 验证检查 → 邮箱确认 → 账户激活                  │
│     ↓                                                       │
│ 引导流程 → 偏好设置 → 数据迁移 → 完成注册                  │
│     ↓                                                       │
│ [状态转换: AUTHENTICATING → AUTHENTICATED]                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    主界面操作阶段                            │
│ [状态: ACTIVE]                                              │
│                                                             │
│ [聊天功能流程]                                              │
│ 选择/创建会话 → 模型配置 → 消息输入 → AI推理                │
│     ↓                                                       │
│ [状态: PROCESSING] → 流式响应 → 结果显示 → [状态: ACTIVE]   │
│                                                             │
│ [知识库功能流程]                                            │
│ 创建知识库 → 文档上传 → 解析处理 → 向量化                  │
│     ↓                                                       │
│ [状态: PROCESSING] → 索引构建 → 完成通知 → [状态: ACTIVE]   │
│                                                             │
│ [模型管理流程]                                              │
│ 浏览模型 → 选择下载 → 下载管理 → 模型部署                  │
│     ↓                                                       │
│ [状态: DOWNLOADING] → 验证安装 → 配置优化 → [状态: ACTIVE]  │
│                                                             │
│ [多模态处理流程]                                            │
│ 文件上传 → 格式检测 → 处理选择 → 执行处理                  │
│     ↓                                                       │
│ [状态: PROCESSING] → 结果生成 → 预览展示 → [状态: ACTIVE]   │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    错误处理阶段                              │
│ [状态: ERROR]                                               │
│                                                             │
│ 错误检测 → 错误分类 → 恢复策略 → 用户通知                  │
│     ↓                                                       │
│ [网络错误] → 重连机制 → 离线模式 → 数据缓存                │
│ [AI错误] → 模型切换 → 参数调整 → 重试机制                  │
│ [系统错误] → 日志记录 → 状态重置 → 安全模式                │
│     ↓                                                       │
│ [状态转换: ERROR → ACTIVE/SAFE_MODE]                        │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    应用退出阶段                              │
│ [状态: SHUTTING_DOWN]                                       │
│                                                             │
│ 保存状态 → 数据同步 → 资源清理 → 服务停止                  │
│     ↓                                                       │
│ 会话保存 → 配置持久化 → 临时文件清理 → 网络断开             │
│     ↓                                                       │
│ [状态转换: SHUTTING_DOWN → TERMINATED]                      │
└─────────────────────────────────────────────────────────────┘
```

#### 7.1.2 状态机设计

```
AI Studio 应用状态机：

状态定义：
┌─────────────────────────────────────────────────────────────┐
│ INITIALIZING    │ 应用启动初始化中                          │
│ READY          │ 初始化完成，等待用户操作                   │
│ AUTHENTICATING │ 用户认证过程中                            │
│ GUEST_MODE     │ 游客模式运行中                            │
│ AUTHENTICATED  │ 用户已认证登录                            │
│ ACTIVE         │ 正常运行状态                              │
│ PROCESSING     │ 后台处理任务中                            │
│ DOWNLOADING    │ 文件下载中                                │
│ ERROR          │ 错误状态                                  │
│ SAFE_MODE      │ 安全模式运行                              │
│ SHUTTING_DOWN  │ 应用关闭中                                │
│ TERMINATED     │ 应用已终止                                │
└─────────────────────────────────────────────────────────────┘

状态转换规则：
INITIALIZING → READY (初始化完成)
READY → AUTHENTICATING (用户选择认证)
AUTHENTICATING → GUEST_MODE (选择游客模式)
AUTHENTICATING → AUTHENTICATED (认证成功)
GUEST_MODE → ACTIVE (进入主功能)
AUTHENTICATED → ACTIVE (进入主功能)
ACTIVE → PROCESSING (开始处理任务)
ACTIVE → DOWNLOADING (开始下载)
PROCESSING → ACTIVE (处理完成)
DOWNLOADING → ACTIVE (下载完成)
任何状态 → ERROR (发生错误)
ERROR → ACTIVE (错误恢复)
ERROR → SAFE_MODE (进入安全模式)
任何状态 → SHUTTING_DOWN (用户退出)
SHUTTING_DOWN → TERMINATED (退出完成)
```

### 7.2 数据处理逻辑

#### 7.2.1 数据流处理架构

```
数据处理完整流程：

数据输入 → 验证清洗 → 格式转换 → 业务处理 → 结果输出 → 持久化存储

数据处理管道：
┌─────────────────────────────────────────────────────────────┐
│                    数据输入层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   用户输入   │ │   文件上传   │ │   API调用   │ │ 系统事件 │ │
│  │ User Input  │ │File Upload  │ │API Request  │ │SysEvent │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    数据验证层                                │
│                                                             │
│ [格式验证] → 数据类型检查 → 结构验证 → 完整性检查           │
│     ↓                                                       │
│ 字段验证 → 范围检查 → 约束验证 → 业务规则                  │
│                                                             │
│ [安全验证] → 输入过滤 → XSS防护 → SQL注入防护              │
│     ↓                                                       │
│ 权限检查 → 访问控制 → 操作审计 → 异常检测                  │
│                                                             │
│ [质量检查] → 数据质量评估 → 重复检测 → 一致性验证           │
│     ↓                                                       │
│ 完整性检查 → 准确性验证 → 时效性检查 → 合规性验证           │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    数据转换层                                │
│                                                             │
│ [格式转换] → 编码转换 → 结构映射 → 标准化处理               │
│     ↓                                                       │
│ JSON/XML转换 → 二进制处理 → 压缩解压 → 加密解密            │
│                                                             │
│ [数据清洗] → 噪声过滤 → 异常值处理 → 缺失值填充             │
│     ↓                                                       │
│ 重复数据去除 → 格式统一 → 编码标准化 → 字段映射             │
│                                                             │
│ [数据增强] → 特征提取 → 关联分析 → 语义理解                 │
│     ↓                                                       │
│ 元数据生成 → 标签标注 → 分类处理 → 索引构建                │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    业务处理层                                │
│                                                             │
│ [AI处理] → 模型推理 → 结果生成 → 质量评估                   │
│     ↓                                                       │
│ 自然语言处理 → 图像识别 → 语音处理 → 多模态融合             │
│                                                             │
│ [知识处理] → 信息抽取 → 关系识别 → 知识图谱                 │
│     ↓                                                       │
│ 语义分析 → 实体识别 → 关系抽取 → 推理计算                  │
│                                                             │
│ [业务逻辑] → 规则引擎 → 工作流程 → 决策支持                 │
│     ↓                                                       │
│ 状态管理 → 事务处理 → 并发控制 → 异常处理                  │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    结果输出层                                │
│ • 格式化输出 • 结果验证 • 性能统计 • 用户反馈               │
│ • 缓存管理 • 版本控制 • 审计日志 • 监控告警                 │
└─────────────────────────────────────────────────────────────┘
```

#### 7.2.2 并发处理机制

```
并发处理架构：

任务队列 → 线程池 → 资源调度 → 结果聚合 → 状态同步

并发处理流程：
┌─────────────────────────────────────────────────────────────┐
│                    任务调度器                                │
│                                                             │
│ [任务分类] → CPU密集型 → GPU计算型 → I/O密集型              │
│     ↓                                                       │
│ 优先级队列 → 高优先级 → 普通优先级 → 低优先级               │
│     ↓                                                       │
│ [资源评估] → CPU使用率 → 内存占用 → GPU利用率               │
│     ↓                                                       │
│ 负载均衡 → 任务分发 → 资源分配 → 执行监控                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    执行引擎                                  │
│                                                             │
│ [线程池管理]                                                │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │  工作线程1   │ │  工作线程2   │ │  工作线程N   │             │
│ │WorkerThread1│ │WorkerThread2│ │WorkerThreadN│             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
│                                                             │
│ [异步处理]                                                  │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │  AI推理任务  │ │  文件处理   │ │  网络请求   │             │
│ │AI Inference │ │File Process │ │Network Req  │             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
│                                                             │
│ [协程管理]                                                  │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │  轻量级任务  │ │  I/O操作    │ │  定时任务   │             │
│ │Lightweight  │ │I/O Operation│ │Scheduled    │             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    同步机制                                  │
│                                                             │
│ [锁机制] → 互斥锁 → 读写锁 → 条件变量 → 信号量               │
│     ↓                                                       │
│ 死锁检测 → 优先级继承 → 锁超时 → 锁统计                    │
│                                                             │
│ [无锁编程] → 原子操作 → CAS操作 → 内存屏障                  │
│     ↓                                                       │
│ 无锁队列 → 无锁栈 → 无锁哈希表 → RCU机制                   │
│                                                             │
│ [消息传递] → 通道通信 → 事件驱动 → 发布订阅                 │
│     ↓                                                       │
│ 异步消息 → 同步消息 → 广播消息 → 组播消息                  │
└─────────────────────────────────────────────────────────────┘
```

### 7.3 **AI推理时序图**

#### 7.3.1 推理执行时序

```
AI推理完整时序图：

用户请求 → 预处理 → 模型加载 → 推理执行 → 后处理 → 结果返回

时间线：
T0: 用户发起推理请求
    │
    ├─ 请求接收和解析 (5ms)
    ├─ 参数验证和预处理 (10ms)
    ├─ 上下文准备 (15ms)
    │
T1: 模型调度阶段 (30ms)
    │
    ├─ 模型选择和验证
    ├─ 资源分配检查
    ├─ 队列管理
    │
T2: 模型加载阶段 (100-2000ms)
    │
    ├─ [热加载路径] → 缓存检查 → 直接使用 (100ms)
    ├─ [冷加载路径] → 文件读取 → 内存加载 → 初始化 (2000ms)
    ├─ [量化加载] → 解压缩 → 格式转换 → 优化 (1500ms)
    │
T3: 推理准备阶段 (50ms)
    │
    ├─ 输入张量准备
    ├─ 设备内存分配
    ├─ 计算图优化
    │
T4: 推理执行阶段 (100-5000ms)
    │
    ├─ [CPU推理] → 多线程计算 → 结果生成 (1000-5000ms)
    ├─ [GPU推理] → 数据传输 → GPU计算 → 结果回传 (100-1000ms)
    ├─ [流式推理] → 增量计算 → 实时输出 (持续)
    │
T5: 后处理阶段 (20ms)
    │
    ├─ 结果解码和格式化
    ├─ 质量检查和过滤
    ├─ 元数据生成
    │
T6: 结果返回 (10ms)
    │
    ├─ 响应封装
    ├─ 状态更新
    ├─ 性能统计

并发推理时序：
┌─────────────────────────────────────────────────────────────┐
│ 请求1 │████████████████████████████████████████████████████│
│ 请求2 │    ████████████████████████████████████████████████│
│ 请求3 │        ████████████████████████████████████████████│
│ 请求4 │            ████████████████████████████████████████│
└─────────────────────────────────────────────────────────────┘
       T0   T1   T2   T3   T4   T5   T6   T7   T8   T9   T10

批处理推理时序：
┌─────────────────────────────────────────────────────────────┐
│ 收集阶段 │████████│                                        │
│ 批处理   │        │████████████████████████████████████████│
│ 分发阶段 │                                                │████│
└─────────────────────────────────────────────────────────────┘
          T0      T1                                      T2  T3
```

---

## 第八部分：API接口设计

### 8.1 **Tauri命令接口规范**

#### 8.1.1 命令接口架构

AI Studio 通过 Tauri 的 IPC 机制实现前后端通信，所有的业务逻辑都通过标准化的命令接口暴露给前端。

**命令接口分类架构：**
```
Tauri命令接口体系：

┌─────────────────────────────────────────────────────────────┐
│                        核心业务接口                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   聊天接口   │ │  知识库接口  │ │   模型接口   │ │ 用户接口 │ │
│  │Chat Commands│ │Knowledge API│ │Model Commands│ │User API │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        扩展功能接口                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  多模态接口  │ │  网络接口   │ │  插件接口   │ │ 系统接口 │ │
│  │Multimodal   │ │Network API  │ │Plugin API   │ │System   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        工具类接口                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  文件接口   │ │  配置接口   │ │  日志接口   │ │ 监控接口 │ │
│  │File System │ │Config API   │ │Logging API  │ │Monitor  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 8.1.2 聊天接口详细设计

```rust
// src/commands/chat.rs - 聊天相关命令接口
use tauri::{command, State, Window};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// ==================== 数据结构定义 ====================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatSession {
    pub id: String,
    pub title: String,
    pub model: String,
    pub system_prompt: Option<String>,
    pub settings: ChatSettings,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub message_count: u32,
    pub tags: Vec<String>,
    pub is_pinned: bool,
    pub is_archived: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatMessage {
    pub id: String,
    pub session_id: String,
    pub role: MessageRole,
    pub content: String,
    pub content_type: ContentType,
    pub attachments: Vec<Attachment>,
    pub metadata: MessageMetadata,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub status: MessageStatus,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ContentType {
    Text,
    Markdown,
    Code,
    Image,
    Audio,
    Video,
    File,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum MessageStatus {
    Sending,
    Sent,
    Processing,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatSettings {
    pub temperature: f32,
    pub max_tokens: u32,
    pub top_p: f32,
    pub frequency_penalty: f32,
    pub presence_penalty: f32,
    pub stop_sequences: Vec<String>,
    pub stream: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MessageMetadata {
    pub tokens_used: Option<u32>,
    pub inference_time: Option<u64>,
    pub model_used: Option<String>,
    pub cost: Option<f64>,
    pub quality_score: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Attachment {
    pub id: String,
    pub filename: String,
    pub file_type: String,
    pub file_size: u64,
    pub file_path: String,
    pub thumbnail_path: Option<String>,
}

// ==================== 请求/响应结构 ====================

#[derive(Debug, Deserialize)]
pub struct CreateSessionRequest {
    pub title: Option<String>,
    pub model: String,
    pub system_prompt: Option<String>,
    pub settings: Option<ChatSettings>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub content: String,
    pub content_type: Option<ContentType>,
    pub attachments: Option<Vec<String>>, // 文件路径列表
    pub parent_message_id: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateSessionRequest {
    pub session_id: String,
    pub title: Option<String>,
    pub settings: Option<ChatSettings>,
    pub tags: Option<Vec<String>>,
    pub is_pinned: Option<bool>,
    pub is_archived: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct ChatResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

// ==================== 命令接口实现 ====================

/// 创建新的聊天会话
#[command]
pub async fn create_chat_session(
    request: CreateSessionRequest,
    state: State<'_, AppState>,
) -> Result<ChatResponse<ChatSession>, String> {
    let chat_service = state.chat_service.lock().await;

    match chat_service.create_session(
        request.title,
        request.model,
        request.system_prompt,
        request.settings.unwrap_or_default(),
        request.tags.unwrap_or_default(),
    ).await {
        Ok(session) => Ok(ChatResponse {
            success: true,
            data: Some(session),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 发送消息到指定会话
#[command]
pub async fn send_chat_message(
    request: SendMessageRequest,
    state: State<'_, AppState>,
    window: Window,
) -> Result<ChatResponse<ChatMessage>, String> {
    let chat_service = state.chat_service.lock().await;

    // 创建用户消息
    let user_message = match chat_service.create_user_message(
        &request.session_id,
        &request.content,
        request.content_type.unwrap_or(ContentType::Text),
        request.attachments.unwrap_or_default(),
        request.parent_message_id,
    ).await {
        Ok(message) => message,
        Err(e) => return Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    };

    // 异步生成AI响应
    let session_id = request.session_id.clone();
    let service = chat_service.clone();
    let window_clone = window.clone();

    tokio::spawn(async move {
        match service.generate_ai_response(&session_id, &user_message).await {
            Ok(ai_message) => {
                // 发送AI响应事件
                let _ = window_clone.emit("ai_message_received", &ai_message);
            }
            Err(e) => {
                // 发送错误事件
                let _ = window_clone.emit("ai_message_error", &e.to_string());
            }
        }
    });

    Ok(ChatResponse {
        success: true,
        data: Some(user_message),
        error: None,
        timestamp: chrono::Utc::now(),
    })
}

/// 获取聊天会话列表
#[command]
pub async fn get_chat_sessions(
    page: Option<u32>,
    page_size: Option<u32>,
    archived: Option<bool>,
    tags: Option<Vec<String>>,
    state: State<'_, AppState>,
) -> Result<ChatResponse<PaginatedResponse<ChatSession>>, String> {
    let chat_service = state.chat_service.lock().await;

    let page = page.unwrap_or(1);
    let page_size = page_size.unwrap_or(20);

    match chat_service.get_sessions(page, page_size, archived, tags).await {
        Ok((sessions, total)) => {
            let has_next = (page * page_size) < total as u32;
            let has_prev = page > 1;

            Ok(ChatResponse {
                success: true,
                data: Some(PaginatedResponse {
                    items: sessions,
                    total,
                    page,
                    page_size,
                    has_next,
                    has_prev,
                }),
                error: None,
                timestamp: chrono::Utc::now(),
            })
        }
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 获取会话消息列表
#[command]
pub async fn get_chat_messages(
    session_id: String,
    page: Option<u32>,
    page_size: Option<u32>,
    before_message_id: Option<String>,
    state: State<'_, AppState>,
) -> Result<ChatResponse<PaginatedResponse<ChatMessage>>, String> {
    let chat_service = state.chat_service.lock().await;

    let page = page.unwrap_or(1);
    let page_size = page_size.unwrap_or(50);

    match chat_service.get_messages(&session_id, page, page_size, before_message_id).await {
        Ok((messages, total)) => {
            let has_next = (page * page_size) < total as u32;
            let has_prev = page > 1;

            Ok(ChatResponse {
                success: true,
                data: Some(PaginatedResponse {
                    items: messages,
                    total,
                    page,
                    page_size,
                    has_next,
                    has_prev,
                }),
                error: None,
                timestamp: chrono::Utc::now(),
            })
        }
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 更新聊天会话
#[command]
pub async fn update_chat_session(
    request: UpdateSessionRequest,
    state: State<'_, AppState>,
) -> Result<ChatResponse<ChatSession>, String> {
    let chat_service = state.chat_service.lock().await;

    match chat_service.update_session(
        &request.session_id,
        request.title,
        request.settings,
        request.tags,
        request.is_pinned,
        request.is_archived,
    ).await {
        Ok(session) => Ok(ChatResponse {
            success: true,
            data: Some(session),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 删除聊天会话
#[command]
pub async fn delete_chat_session(
    session_id: String,
    state: State<'_, AppState>,
) -> Result<ChatResponse<bool>, String> {
    let chat_service = state.chat_service.lock().await;

    match chat_service.delete_session(&session_id).await {
        Ok(_) => Ok(ChatResponse {
            success: true,
            data: Some(true),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 搜索聊天消息
#[command]
pub async fn search_chat_messages(
    query: String,
    session_ids: Option<Vec<String>>,
    content_types: Option<Vec<ContentType>>,
    date_range: Option<(chrono::DateTime<chrono::Utc>, chrono::DateTime<chrono::Utc>)>,
    page: Option<u32>,
    page_size: Option<u32>,
    state: State<'_, AppState>,
) -> Result<ChatResponse<PaginatedResponse<ChatMessage>>, String> {
    let chat_service = state.chat_service.lock().await;

    let page = page.unwrap_or(1);
    let page_size = page_size.unwrap_or(20);

    match chat_service.search_messages(
        &query,
        session_ids,
        content_types,
        date_range,
        page,
        page_size,
    ).await {
        Ok((messages, total)) => {
            let has_next = (page * page_size) < total as u32;
            let has_prev = page > 1;

            Ok(ChatResponse {
                success: true,
                data: Some(PaginatedResponse {
                    items: messages,
                    total,
                    page,
                    page_size,
                    has_next,
                    has_prev,
                }),
                error: None,
                timestamp: chrono::Utc::now(),
            })
        }
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}
```

### 8.2 **知识库接口设计**

#### 8.2.1 知识库管理接口

```rust
// src/commands/knowledge.rs - 知识库相关命令接口
use tauri::{command, State, Window};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

// ==================== 数据结构定义 ====================

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct KnowledgeBase {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub embedding_model: String,
    pub chunk_strategy: ChunkStrategy,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub document_count: u32,
    pub total_chunks: u64,
    pub storage_size: u64,
    pub is_public: bool,
    pub tags: Vec<String>,
    pub settings: KnowledgeBaseSettings,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Document {
    pub id: String,
    pub knowledge_base_id: String,
    pub filename: String,
    pub file_path: String,
    pub file_size: u64,
    pub file_type: String,
    pub mime_type: String,
    pub content_hash: String,
    pub chunk_count: u32,
    pub processing_status: ProcessingStatus,
    pub error_message: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub processed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub metadata: DocumentMetadata,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ProcessingStatus {
    Pending,
    Processing,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ChunkStrategy {
    FixedSize { size: usize, overlap: usize },
    Semantic { max_size: usize, min_size: usize },
    Structural { preserve_hierarchy: bool },
    Hybrid { primary: Box<ChunkStrategy>, fallback: Box<ChunkStrategy> },
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct KnowledgeBaseSettings {
    pub chunk_size: usize,
    pub chunk_overlap: usize,
    pub embedding_batch_size: usize,
    pub similarity_threshold: f32,
    pub max_search_results: usize,
    pub enable_reranking: bool,
    pub auto_update: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DocumentMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub created_date: Option<chrono::DateTime<chrono::Utc>>,
    pub language: Option<String>,
    pub keywords: Vec<String>,
    pub summary: Option<String>,
    pub page_count: Option<u32>,
    pub word_count: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchResult {
    pub chunk_id: String,
    pub document_id: String,
    pub content: String,
    pub similarity_score: f32,
    pub metadata: ChunkMetadata,
    pub highlights: Vec<TextHighlight>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChunkMetadata {
    pub chunk_index: u32,
    pub start_offset: usize,
    pub end_offset: usize,
    pub page_number: Option<u32>,
    pub section_title: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TextHighlight {
    pub start: usize,
    pub end: usize,
    pub text: String,
}

// ==================== 请求/响应结构 ====================

#[derive(Debug, Deserialize)]
pub struct CreateKnowledgeBaseRequest {
    pub name: String,
    pub description: Option<String>,
    pub embedding_model: String,
    pub chunk_strategy: ChunkStrategy,
    pub settings: Option<KnowledgeBaseSettings>,
    pub tags: Option<Vec<String>>,
    pub is_public: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UploadDocumentsRequest {
    pub knowledge_base_id: String,
    pub file_paths: Vec<String>,
    pub chunk_strategy: Option<ChunkStrategy>,
    pub metadata: Option<DocumentMetadata>,
    pub auto_process: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct SearchDocumentsRequest {
    pub knowledge_base_id: String,
    pub query: String,
    pub max_results: Option<usize>,
    pub similarity_threshold: Option<f32>,
    pub filters: Option<SearchFilters>,
    pub enable_reranking: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct SearchFilters {
    pub document_types: Option<Vec<String>>,
    pub date_range: Option<(chrono::DateTime<chrono::Utc>, chrono::DateTime<chrono::Utc>)>,
    pub authors: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
    pub languages: Option<Vec<String>>,
}

#[derive(Debug, Serialize)]
pub struct ProcessingProgress {
    pub task_id: String,
    pub status: ProcessingStatus,
    pub progress_percentage: f32,
    pub current_step: String,
    pub estimated_remaining: Option<u64>, // 秒
    pub processed_chunks: u32,
    pub total_chunks: u32,
    pub error_message: Option<String>,
}

// ==================== 命令接口实现 ====================

/// 创建知识库
#[command]
pub async fn create_knowledge_base(
    request: CreateKnowledgeBaseRequest,
    state: State<'_, AppState>,
) -> Result<ChatResponse<KnowledgeBase>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    match knowledge_service.create_knowledge_base(
        &request.name,
        request.description.as_deref(),
        &request.embedding_model,
        request.chunk_strategy,
        request.settings.unwrap_or_default(),
        request.tags.unwrap_or_default(),
        request.is_public.unwrap_or(false),
    ).await {
        Ok(kb) => Ok(ChatResponse {
            success: true,
            data: Some(kb),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 上传文档到知识库
#[command]
pub async fn upload_documents(
    request: UploadDocumentsRequest,
    state: State<'_, AppState>,
    window: Window,
) -> Result<ChatResponse<Vec<String>>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    match knowledge_service.upload_documents(
        &request.knowledge_base_id,
        request.file_paths,
        request.chunk_strategy,
        request.metadata,
        request.auto_process.unwrap_or(true),
    ).await {
        Ok(task_ids) => {
            // 启动进度监控
            for task_id in &task_ids {
                let task_id_clone = task_id.clone();
                let window_clone = window.clone();
                let service_clone = knowledge_service.clone();

                tokio::spawn(async move {
                    loop {
                        match service_clone.get_processing_progress(&task_id_clone).await {
                            Ok(progress) => {
                                let _ = window_clone.emit("document_processing_progress", &progress);

                                if matches!(progress.status, ProcessingStatus::Completed | ProcessingStatus::Failed | ProcessingStatus::Cancelled) {
                                    break;
                                }
                            }
                            Err(_) => break,
                        }

                        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
                    }
                });
            }

            Ok(ChatResponse {
                success: true,
                data: Some(task_ids),
                error: None,
                timestamp: chrono::Utc::now(),
            })
        }
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 搜索文档
#[command]
pub async fn search_documents(
    request: SearchDocumentsRequest,
    state: State<'_, AppState>,
) -> Result<ChatResponse<Vec<SearchResult>>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    match knowledge_service.search_documents(
        &request.knowledge_base_id,
        &request.query,
        request.max_results.unwrap_or(10),
        request.similarity_threshold.unwrap_or(0.7),
        request.filters,
        request.enable_reranking.unwrap_or(true),
    ).await {
        Ok(results) => Ok(ChatResponse {
            success: true,
            data: Some(results),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 获取知识库列表
#[command]
pub async fn get_knowledge_bases(
    page: Option<u32>,
    page_size: Option<u32>,
    tags: Option<Vec<String>>,
    is_public: Option<bool>,
    state: State<'_, AppState>,
) -> Result<ChatResponse<PaginatedResponse<KnowledgeBase>>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    let page = page.unwrap_or(1);
    let page_size = page_size.unwrap_or(20);

    match knowledge_service.get_knowledge_bases(page, page_size, tags, is_public).await {
        Ok((knowledge_bases, total)) => {
            let has_next = (page * page_size) < total as u32;
            let has_prev = page > 1;

            Ok(ChatResponse {
                success: true,
                data: Some(PaginatedResponse {
                    items: knowledge_bases,
                    total,
                    page,
                    page_size,
                    has_next,
                    has_prev,
                }),
                error: None,
                timestamp: chrono::Utc::now(),
            })
        }
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 获取文档列表
#[command]
pub async fn get_documents(
    knowledge_base_id: String,
    page: Option<u32>,
    page_size: Option<u32>,
    status_filter: Option<ProcessingStatus>,
    state: State<'_, AppState>,
) -> Result<ChatResponse<PaginatedResponse<Document>>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    let page = page.unwrap_or(1);
    let page_size = page_size.unwrap_or(20);

    match knowledge_service.get_documents(&knowledge_base_id, page, page_size, status_filter).await {
        Ok((documents, total)) => {
            let has_next = (page * page_size) < total as u32;
            let has_prev = page > 1;

            Ok(ChatResponse {
                success: true,
                data: Some(PaginatedResponse {
                    items: documents,
                    total,
                    page,
                    page_size,
                    has_next,
                    has_prev,
                }),
                error: None,
                timestamp: chrono::Utc::now(),
            })
        }
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 删除知识库
#[command]
pub async fn delete_knowledge_base(
    knowledge_base_id: String,
    force: Option<bool>,
    state: State<'_, AppState>,
) -> Result<ChatResponse<bool>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    match knowledge_service.delete_knowledge_base(&knowledge_base_id, force.unwrap_or(false)).await {
        Ok(_) => Ok(ChatResponse {
            success: true,
            data: Some(true),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 删除文档
#[command]
pub async fn delete_document(
    document_id: String,
    state: State<'_, AppState>,
) -> Result<ChatResponse<bool>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    match knowledge_service.delete_document(&document_id).await {
        Ok(_) => Ok(ChatResponse {
            success: true,
            data: Some(true),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}

/// 获取处理进度
#[command]
pub async fn get_processing_progress(
    task_id: String,
    state: State<'_, AppState>,
) -> Result<ChatResponse<ProcessingProgress>, String> {
    let knowledge_service = state.knowledge_service.lock().await;

    match knowledge_service.get_processing_progress(&task_id).await {
        Ok(progress) => Ok(ChatResponse {
            success: true,
            data: Some(progress),
            error: None,
            timestamp: chrono::Utc::now(),
        }),
        Err(e) => Ok(ChatResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
            timestamp: chrono::Utc::now(),
        }),
    }
}
```

---

## 第九部分：详细界面交互设计

### 9.1 **聊天界面交互流程**

#### 9.1.1 聊天界面布局设计

```
AI Studio 聊天界面完整布局：

┌─────────────────────────────────────────────────────────────┐
│                        标题栏区域                            │
│ [Logo] AI Studio    [会话标题]    [设置] [最小化] [关闭]     │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 侧边栏 │                    主聊天区域                       │
│        │                                                   │
│ ┌─────┐│ ┌─────────────────────────────────────────────────┐ │
│ │会话1││ │                消息显示区域                      │ │
│ │会话2││ │ ┌─────────────────────────────────────────────┐ │ │
│ │会话3││ │ │ [用户头像] 用户消息内容...                   │ │ │
│ │ +新 ││ │ │            [时间戳] [操作按钮]              │ │ │
│ │会话 ││ │ └─────────────────────────────────────────────┘ │ │
│ │     ││ │                                                 │ │
│ │[设置]││ │ ┌─────────────────────────────────────────────┐ │ │
│ │[帮助]││ │ │                   [AI头像] AI回复内容...     │ │ │
│ │[关于]││ │ │              [时间戳] [操作按钮]            │ │ │
│ └─────┘│ │ └─────────────────────────────────────────────┘ │ │
│        │ │                                                 │ │
│        │ │ ┌─────────────────────────────────────────────┐ │ │
│        │ │ │ [正在输入指示器...]                          │ │ │
│        │ │ └─────────────────────────────────────────────┘ │ │
│        │ └─────────────────────────────────────────────────┘ │
│        │                                                   │
│        │ ┌─────────────────────────────────────────────────┐ │
│        │ │                输入区域                          │ │
│        │ │ ┌─────────────────────────────────────────────┐ │ │
│        │ │ │ [附件] [表情] 输入框... [语音] [发送]        │ │ │
│        │ │ └─────────────────────────────────────────────┘ │ │
│        │ │ [模型选择] [参数设置] [知识库] [插件]           │ │
│        │ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

响应式布局适配：
┌─────────────────────────────────────────────────────────────┐
│ 桌面端 (≥1024px)                                            │
│ • 三栏布局：侧边栏 + 主聊天区 + 右侧面板                    │
│ • 侧边栏宽度：280px                                         │
│ • 主聊天区：自适应                                          │
│ • 右侧面板：320px（可折叠）                                 │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 平板端 (768px-1023px)                                       │
│ • 两栏布局：侧边栏 + 主聊天区                               │
│ • 侧边栏：可折叠抽屉式                                      │
│ • 主聊天区：全宽度                                          │
│ • 右侧面板：底部弹出式                                      │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 手机端 (<768px)                                             │
│ • 单栏布局：全屏聊天                                        │
│ • 侧边栏：全屏抽屉                                          │
│ • 输入区：底部固定                                          │
│ • 操作按钮：简化显示                                        │
└─────────────────────────────────────────────────────────────┘
```

#### 9.1.2 消息交互状态图

```
消息交互完整状态流程：

用户输入 → 发送验证 → 消息发送 → AI处理 → 流式响应 → 完成显示

消息状态转换：
┌─────────────────────────────────────────────────────────────┐
│                    输入状态 (INPUT)                          │
│ [用户行为]                                                  │
│ • 文本输入 • 文件拖拽 • 语音录制 • 图片粘贴                 │
│ • 快捷命令 • 模板选择 • 历史引用 • 知识库搜索               │
│                                                             │
│ [状态指示]                                                  │
│ • 输入框激活 • 字符计数 • 格式预览 • 附件列表               │
│ • 建议提示 • 错误提示 • 快捷操作 • 发送按钮状态             │
│                                                             │
│ [转换条件]                                                  │
│ • 点击发送 → SENDING                                        │
│ • 按Enter → SENDING                                         │
│ • 快捷键 → SENDING                                          │
│ • 清空输入 → IDLE                                           │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    发送状态 (SENDING)                        │
│ [处理流程]                                                  │
│ • 输入验证 • 内容过滤 • 格式转换 • 附件上传                 │
│ • 权限检查 • 配额验证 • 网络检测 • 服务可用性               │
│                                                             │
│ [视觉反馈]                                                  │
│ • 发送按钮禁用 • 加载动画 • 进度指示 • 消息预览             │
│ • 取消按钮 • 状态文本 • 错误提示 • 重试选项                 │
│                                                             │
│ [转换条件]                                                  │
│ • 发送成功 → SENT                                           │
│ • 发送失败 → FAILED                                         │
│ • 用户取消 → CANCELLED                                      │
│ • 网络错误 → ERROR                                          │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    已发送状态 (SENT)                         │
│ [消息显示]                                                  │
│ • 消息气泡 • 发送时间 • 发送状态 • 用户头像                 │
│ • 消息内容 • 附件预览 • 操作菜单 • 引用按钮                 │
│                                                             │
│ [AI处理指示]                                                │
│ • "AI正在思考..." • 打字动画 • 进度条 • 预计时间            │
│ • 模型信息 • 处理状态 • 取消按钮 • 优先级                  │
│                                                             │
│ [转换条件]                                                  │
│ • AI开始响应 → AI_RESPONDING                                │
│ • 处理超时 → TIMEOUT                                        │
│ • 处理错误 → AI_ERROR                                       │
│ • 用户取消 → CANCELLED                                      │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    AI响应状态 (AI_RESPONDING)                │
│ [流式显示]                                                  │
│ • 逐字显示 • 打字效果 • 实时渲染 • 格式解析                 │
│ • 代码高亮 • 表格渲染 • 图片显示 • 链接处理                 │
│                                                             │
│ [交互控制]                                                  │
│ • 暂停按钮 • 停止按钮 • 速度控制 • 跳过动画                 │
│ • 复制按钮 • 重新生成 • 继续生成 • 分支对话                 │
│                                                             │
│ [状态监控]                                                  │
│ • 生成进度 • Token计数 • 响应时间 • 质量评分                │
│ • 成本统计 • 模型负载 • 错误检测 • 性能指标                 │
│                                                             │
│ [转换条件]                                                  │
│ • 响应完成 → COMPLETED                                      │
│ • 用户停止 → STOPPED                                        │
│ • 生成错误 → AI_ERROR                                       │
│ • 网络中断 → NETWORK_ERROR                                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    完成状态 (COMPLETED)                      │
│ [最终显示]                                                  │
│ • 完整消息 • 格式化内容 • 语法高亮 • 媒体渲染               │
│ • 时间戳 • 统计信息 • 质量评分 • 操作按钮                  │
│                                                             │
│ [后续操作]                                                  │
│ • 复制内容 • 编辑消息 • 删除消息 • 分享消息                 │
│ • 点赞评分 • 举报内容 • 保存收藏 • 继续对话                 │
│                                                             │
│ [数据处理]                                                  │
│ • 消息保存 • 索引更新 • 统计记录 • 缓存管理                 │
│ • 备份同步 • 搜索索引 • 关联分析 • 质量评估                 │
│                                                             │
│ [转换条件]                                                  │
│ • 新消息输入 → INPUT                                        │
│ • 重新生成 → AI_RESPONDING                                  │
│ • 编辑消息 → EDITING                                        │
│ • 删除消息 → DELETED                                        │
└─────────────────────────────────────────────────────────────┘

错误状态处理：
┌─────────────────────────────────────────────────────────────┐
│ ERROR状态 → 错误分类 → 恢复策略 → 用户提示 → 重试机制       │
│                                                             │
│ [网络错误] → 自动重连 → 离线提示 → 队列缓存 → 恢复发送      │
│ [AI错误] → 模型切换 → 参数调整 → 错误说明 → 重新生成        │
│ [系统错误] → 日志记录 → 安全模式 → 技术支持 → 问题反馈      │
│ [权限错误] → 登录提示 → 升级建议 → 功能限制 → 替代方案      │
└─────────────────────────────────────────────────────────────┘
```

### 9.2 **知识库界面设计**

#### 9.2.1 知识库管理界面布局

```
知识库管理界面完整布局：

┌─────────────────────────────────────────────────────────────┐
│                        导航栏                                │
│ [返回] 知识库管理    [搜索框]    [新建] [导入] [设置]        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 筛选栏 │                    主内容区域                       │
│        │                                                   │
│ ┌─────┐│ ┌─────────────────────────────────────────────────┐ │
│ │分类 ││ │                知识库列表                        │ │
│ │全部 ││ │ ┌─────────────────────────────────────────────┐ │ │
│ │文档 ││ │ │ [图标] 知识库名称                            │ │ │
│ │代码 ││ │ │ 描述信息...                                 │ │ │
│ │图片 ││ │ │ [文档数] [大小] [更新时间] [操作菜单]        │ │ │
│ │音频 ││ │ └─────────────────────────────────────────────┘ │ │
│ │视频 ││ │                                                 │ │
│ │     ││ │ ┌─────────────────────────────────────────────┐ │ │
│ │标签 ││ │ │ [图标] 另一个知识库                          │ │ │
│ │工作 ││ │ │ 描述信息...                                 │ │ │
│ │学习 ││ │ │ [文档数] [大小] [更新时间] [操作菜单]        │ │ │
│ │项目 ││ │ └─────────────────────────────────────────────┘ │ │
│ │     ││ │                                                 │ │
│ │状态 ││ │ ┌─────────────────────────────────────────────┐ │ │
│ │活跃 ││ │ │ [+] 创建新知识库                             │ │ │
│ │处理 ││ │ └─────────────────────────────────────────────┘ │ │
│ │错误 ││ │                                                 │ │
│ └─────┘│ └─────────────────────────────────────────────────┘ │
│        │                                                   │
│        │ ┌─────────────────────────────────────────────────┐ │
│        │ │                分页控制                          │ │
│        │ │ [上一页] 1 2 3 ... 10 [下一页] 共100个知识库    │ │
│        │ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

知识库详情界面：
┌─────────────────────────────────────────────────────────────┐
│ [返回] 知识库名称    [编辑] [分享] [删除] [更多操作]         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│ 信息面板 │                    文档管理区域                   │
│          │                                                 │
│ ┌───────┐│ ┌─────────────────────────────────────────────┐ │
│ │基本信息││ │                文档列表                      │ │
│ │名称   ││ │ ┌─────────────────────────────────────────┐ │ │
│ │描述   ││ │ │ [图标] 文档名称.pdf                      │ │ │
│ │创建时间││ │ │ 大小: 2.5MB | 状态: 已处理 | 块数: 156   │ │ │
│ │更新时间││ │ │ [预览] [下载] [重新处理] [删除]          │ │ │
│ │       ││ │ └─────────────────────────────────────────┘ │ │
│ │统计信息││ │                                             │ │
│ │文档数量││ │ ┌─────────────────────────────────────────┐ │ │
│ │总大小 ││ │ │ [图标] 另一个文档.docx                   │ │ │
│ │块数量 ││ │ │ 大小: 1.2MB | 状态: 处理中 | 进度: 65%   │ │ │
│ │向量数 ││ │ │ [取消] [查看日志] [重试]                 │ │ │
│ │       ││ │ └─────────────────────────────────────────┘ │ │
│ │配置信息││ │                                             │ │
│ │模型   ││ │ ┌─────────────────────────────────────────┐ │ │
│ │分块策略││ │ │ [拖拽区域] 拖拽文件到此处上传             │ │ │
│ │相似度 ││ │ │ 或 [选择文件] [批量上传]                 │ │ │
│ └───────┘│ │ 支持: PDF, DOC, TXT, MD, HTML...         │ │ │
│          │ └─────────────────────────────────────────────┘ │ │
│          │                                                 │
│          │ ┌─────────────────────────────────────────────┐ │
│          │ │                搜索测试                      │ │
│          │ │ [搜索框] 输入查询内容...        [搜索]       │ │
│          │ │                                             │ │
│          │ │ 搜索结果:                                   │ │
│          │ │ • 相关内容片段1 (相似度: 0.95)              │ │
│          │ │ • 相关内容片段2 (相似度: 0.87)              │ │
│          │ │ • 相关内容片段3 (相似度: 0.82)              │ │
│          │ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 9.2.2 文档处理流程界面

```
文档处理流程可视化界面：

┌─────────────────────────────────────────────────────────────┐
│                    文档处理监控面板                          │
│ [暂停全部] [取消全部] [清理完成] [导出日志]                  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    处理队列视图                              │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 正在处理 (2/5)                                          │ │
│ │                                                         │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ [📄] 技术文档.pdf                                   │ │ │
│ │ │ 进度: ████████████████████████████████████████ 85%  │ │ │
│ │ │ 状态: 正在向量化 (第3步/共4步)                       │ │ │
│ │ │ 已处理: 156/183 块 | 预计剩余: 2分钟                │ │ │
│ │ │ [暂停] [取消] [查看详情]                            │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │                                                         │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ [📊] 数据报告.xlsx                                  │ │ │
│ │ │ 进度: ████████████████████████████████████████ 45%  │ │ │
│ │ │ 状态: 正在解析表格 (第2步/共4步)                    │ │ │
│ │ │ 已处理: 23/51 工作表 | 预计剩余: 5分钟              │ │ │
│ │ │ [暂停] [取消] [查看详情]                            │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 等待队列 (3)                                            │ │
│ │                                                         │ │
│ │ • [📝] 会议记录.docx (优先级: 高)                       │ │
│ │ • [🖼️] 产品图片.zip (优先级: 中)                        │ │
│ │ • [🎵] 音频文件.mp3 (优先级: 低)                        │ │
│ │                                                         │ │
│ │ [调整优先级] [批量操作]                                 │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 已完成 (12) | 失败 (1)                                  │ │
│ │                                                         │ │
│ │ ✅ 用户手册.pdf (2分钟前完成, 234块)                    │ │
│ │ ✅ API文档.md (5分钟前完成, 89块)                       │ │
│ │ ✅ 设计规范.sketch (8分钟前完成, 156块)                 │ │
│ │ ❌ 损坏文件.pdf (10分钟前失败: 文件格式错误)            │ │
│ │                                                         │ │
│ │ [查看全部] [重试失败] [清理记录]                        │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

处理详情弹窗：
┌─────────────────────────────────────────────────────────────┐
│ 文档处理详情 - 技术文档.pdf                    [关闭]       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 基本信息:                                                   │
│ • 文件大小: 2.5 MB                                          │
│ • 页数: 45 页                                               │
│ • 语言: 中文                                                │
│ • 开始时间: 2024-01-15 14:30:25                             │
│                                                             │
│ 处理步骤:                                                   │
│ ✅ 1. 文件验证 (完成 - 0.5秒)                               │
│ ✅ 2. 内容提取 (完成 - 15.2秒)                              │
│ 🔄 3. 文本分块 (进行中 - 已用时45.8秒)                      │
│    ├─ 语义分析: ████████████████████████████████████ 85%    │
│    ├─ 块边界检测: ████████████████████████████████ 78%      │
│    └─ 质量验证: ████████████████████████ 65%                │
│ ⏳ 4. 向量化处理 (等待中)                                   │
│                                                             │
│ 统计信息:                                                   │
│ • 已生成块数: 156 / 183                                     │
│ • 平均块大小: 512 字符                                      │
│ • 重叠区域: 50 字符                                         │
│ • 质量评分: 92.5%                                           │
│                                                             │
│ 错误日志: (无)                                              │
│                                                             │
│ [暂停处理] [取消处理] [导出日志] [技术支持]                  │
└─────────────────────────────────────────────────────────────┘
```

---

## 第十部分：错误处理机制

### 10.1 **错误分类与处理策略**

#### 10.1.1 错误分类体系

```
AI Studio 错误分类体系：

┌─────────────────────────────────────────────────────────────┐
│                        系统级错误                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  启动错误   │ │  内存错误   │ │  文件错误   │ │ 权限错误 │ │
│  │BootupError  │ │MemoryError  │ │ FileError   │ │AuthError│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        网络级错误                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  连接错误   │ │  超时错误   │ │  协议错误   │ │ 认证错误 │ │
│  │ConnectError │ │TimeoutError │ │ProtocolErr  │ │AuthFail │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        业务级错误                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   AI错误    │ │  数据错误   │ │  逻辑错误   │ │ 配置错误 │ │
│  │  AIError    │ │ DataError   │ │LogicError   │ │ConfigErr│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        用户级错误                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  输入错误   │ │  操作错误   │ │  格式错误   │ │ 权限错误 │ │
│  │ InputError  │ │OperationErr │ │FormatError  │ │PermError│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘

错误严重级别：
┌─────────────────────────────────────────────────────────────┐
│ CRITICAL (致命)  │ 系统崩溃、数据丢失、安全漏洞              │
│ ERROR (错误)     │ 功能失效、操作失败、异常中断              │
│ WARNING (警告)   │ 性能下降、资源不足、配置问题              │
│ INFO (信息)      │ 状态变更、操作记录、调试信息              │
│ DEBUG (调试)     │ 详细跟踪、变量状态、执行路径              │
└─────────────────────────────────────────────────────────────┘
```

#### 10.1.2 错误处理流程

```
错误处理完整流程：

错误检测 → 错误分类 → 恢复策略 → 用户通知 → 日志记录 → 监控告警

错误处理管道：
┌─────────────────────────────────────────────────────────────┐
│                    错误检测层                                │
│                                                             │
│ [主动检测] → 异常捕获 → 状态监控 → 健康检查                 │
│     ↓                                                       │
│ try-catch块 → 错误边界 → 断言检查 → 合约验证               │
│                                                             │
│ [被动检测] → 用户反馈 → 日志分析 → 监控告警                 │
│     ↓                                                       │
│ 错误报告 → 性能异常 → 资源耗尽 → 超时检测                  │
│                                                             │
│ [预防检测] → 输入验证 → 权限检查 → 资源评估                 │
│     ↓                                                       │
│ 格式验证 → 范围检查 → 依赖验证 → 配置检查                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    错误分析层                                │
│                                                             │
│ [错误分类] → 类型识别 → 严重程度 → 影响范围                 │
│     ↓                                                       │
│ 系统错误 → 网络错误 → 业务错误 → 用户错误                  │
│                                                             │
│ [根因分析] → 调用栈分析 → 上下文收集 → 关联分析             │
│     ↓                                                       │
│ 错误源定位 → 传播路径 → 触发条件 → 环境因素                │
│                                                             │
│ [影响评估] → 功能影响 → 数据影响 → 用户影响                 │
│     ↓                                                       │
│ 可用性评估 → 性能影响 → 安全风险 → 业务损失                │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    恢复策略层                                │
│                                                             │
│ [自动恢复] → 重试机制 → 降级服务 → 故障转移                 │
│     ↓                                                       │
│ 指数退避 → 断路器 → 限流控制 → 缓存回退                    │
│                                                             │
│ [手动恢复] → 用户引导 → 操作建议 → 技术支持                 │
│     ↓                                                       │
│ 错误提示 → 解决方案 → 联系方式 → 问题反馈                  │
│                                                             │
│ [系统恢复] → 服务重启 → 数据修复 → 配置重置                 │
│     ↓                                                       │
│ 安全模式 → 备份恢复 → 版本回滚 → 紧急维护                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                    通知反馈层                                │
│ • 用户界面通知 • 系统日志记录 • 监控告警 • 开发者通知       │
│ • 错误报告生成 • 统计分析 • 趋势预测 • 改进建议             │
└─────────────────────────────────────────────────────────────┘
```

### 10.2 **异常恢复机制**

#### 10.2.1 重试策略设计

```rust
// src/utils/retry.rs - 重试机制实现
use std::time::Duration;
use tokio::time::sleep;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
    pub jitter: bool,
    pub retry_on: Vec<ErrorType>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorType {
    Network,
    Timeout,
    RateLimited,
    ServerError,
    Temporary,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackoffStrategy {
    Fixed,
    Linear,
    Exponential,
    ExponentialWithJitter,
}

pub struct RetryManager {
    config: RetryConfig,
    strategy: BackoffStrategy,
}

impl RetryManager {
    pub fn new(config: RetryConfig, strategy: BackoffStrategy) -> Self {
        Self { config, strategy }
    }

    // 执行带重试的操作
    pub async fn execute_with_retry<F, T, E>(&self, operation: F) -> Result<T, E>
    where
        F: Fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, E>> + Send>>,
        E: std::fmt::Debug + Clone,
    {
        let mut attempt = 1;
        let mut last_error = None;

        while attempt <= self.config.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    // 检查是否应该重试
                    if !self.should_retry(&error, attempt) {
                        return Err(error);
                    }

                    last_error = Some(error);

                    // 计算延迟时间
                    let delay = self.calculate_delay(attempt);

                    // 记录重试日志
                    log::warn!(
                        "Operation failed on attempt {}/{}, retrying in {:?}",
                        attempt,
                        self.config.max_attempts,
                        delay
                    );

                    // 等待后重试
                    sleep(delay).await;
                    attempt += 1;
                }
            }
        }

        // 所有重试都失败了
        Err(last_error.unwrap())
    }

    // 判断是否应该重试
    fn should_retry<E>(&self, error: &E, attempt: u32) -> bool {
        if attempt >= self.config.max_attempts {
            return false;
        }

        // 这里需要根据具体错误类型判断
        // 示例实现，实际需要根据错误类型匹配
        true
    }

    // 计算延迟时间
    fn calculate_delay(&self, attempt: u32) -> Duration {
        let delay = match self.strategy {
            BackoffStrategy::Fixed => self.config.base_delay,
            BackoffStrategy::Linear => {
                Duration::from_millis(
                    self.config.base_delay.as_millis() as u64 * attempt as u64
                )
            }
            BackoffStrategy::Exponential => {
                let multiplier = self.config.backoff_multiplier.powi(attempt as i32 - 1);
                Duration::from_millis(
                    (self.config.base_delay.as_millis() as f64 * multiplier) as u64
                )
            }
            BackoffStrategy::ExponentialWithJitter => {
                let base_delay = self.calculate_exponential_delay(attempt);
                if self.config.jitter {
                    self.add_jitter(base_delay)
                } else {
                    base_delay
                }
            }
        };

        // 确保不超过最大延迟
        std::cmp::min(delay, self.config.max_delay)
    }

    fn calculate_exponential_delay(&self, attempt: u32) -> Duration {
        let multiplier = self.config.backoff_multiplier.powi(attempt as i32 - 1);
        Duration::from_millis(
            (self.config.base_delay.as_millis() as f64 * multiplier) as u64
        )
    }

    fn add_jitter(&self, delay: Duration) -> Duration {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let jitter_factor = rng.gen_range(0.5..1.5);
        Duration::from_millis((delay.as_millis() as f64 * jitter_factor) as u64)
    }
}

// 预定义的重试配置
impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_millis(1000),
            max_delay: Duration::from_secs(30),
            backoff_multiplier: 2.0,
            jitter: true,
            retry_on: vec![
                ErrorType::Network,
                ErrorType::Timeout,
                ErrorType::ServerError,
                ErrorType::Temporary,
            ],
        }
    }
}

// 网络请求重试配置
pub fn network_retry_config() -> RetryConfig {
    RetryConfig {
        max_attempts: 5,
        base_delay: Duration::from_millis(500),
        max_delay: Duration::from_secs(10),
        backoff_multiplier: 1.5,
        jitter: true,
        retry_on: vec![ErrorType::Network, ErrorType::Timeout],
    }
}

// AI推理重试配置
pub fn ai_inference_retry_config() -> RetryConfig {
    RetryConfig {
        max_attempts: 3,
        base_delay: Duration::from_secs(2),
        max_delay: Duration::from_secs(60),
        backoff_multiplier: 2.0,
        jitter: false,
        retry_on: vec![ErrorType::ServerError, ErrorType::RateLimited],
    }
}

// 文件操作重试配置
pub fn file_operation_retry_config() -> RetryConfig {
    RetryConfig {
        max_attempts: 2,
        base_delay: Duration::from_millis(100),
        max_delay: Duration::from_secs(1),
        backoff_multiplier: 2.0,
        jitter: false,
        retry_on: vec![ErrorType::Temporary],
    }
}
```

---

## 第十一部分：整体架构设计

### 11.1 **系统架构总览**

#### 11.1.1 分层架构设计

```
AI Studio 分层架构总览：

┌─────────────────────────────────────────────────────────────┐
│                        表现层 (Presentation Layer)          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   Vue3 UI   │ │  组件库     │ │  路由管理   │ │ 状态管理 │ │
│  │  Components │ │Design System│ │Vue Router   │ │  Pinia  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  主题系统   │ │  国际化     │ │  响应式设计  │ │ 无障碍   │ │
│  │Theme System │ │    i18n     │ │ Responsive  │ │A11y     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ IPC通信
┌─────────────────────────────────────────────────────────────┐
│                        接口层 (Interface Layer)             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ Tauri命令   │ │  事件系统   │ │  权限管理   │ │ 错误处理 │ │
│  │  Commands   │ │   Events    │ │Permissions  │ │ErrorHdl │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  数据验证   │ │  序列化     │ │  缓存管理   │ │ 监控统计 │ │
│  │ Validation  │ │Serialization│ │Cache Mgmt   │ │Metrics  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        业务层 (Business Layer)              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  聊天服务   │ │  知识库服务  │ │  模型服务   │ │ 用户服务 │ │
│  │ChatService  │ │Knowledge Svc│ │Model Service│ │UserSvc  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 多模态服务   │ │  网络服务   │ │  插件服务   │ │ 系统服务 │ │
│  │Multimodal   │ │Network Svc  │ │Plugin Svc   │ │SystemSvc│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        引擎层 (Engine Layer)                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  AI推理引擎  │ │  向量引擎   │ │  搜索引擎   │ │ 处理引擎 │ │
│  │AI Inference │ │Vector Engine│ │Search Engine│ │ProcessEng│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  渲染引擎   │ │  下载引擎   │ │  同步引擎   │ │ 监控引擎 │ │
│  │Render Engine│ │Download Eng │ │Sync Engine  │ │MonitorEng│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        数据层 (Data Layer)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  ChromaDB   │ │  文件系统   │ │ 缓存系统 │ │
│  │  Database   │ │Vector Store │ │File System  │ │Cache Sys│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  配置存储   │ │  日志存储   │ │  备份存储   │ │ 临时存储 │ │
│  │Config Store │ │Log Storage  │ │Backup Store │ │TempStore│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        基础设施层 (Infrastructure Layer)     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  操作系统   │ │  硬件抽象   │ │  网络协议   │ │ 安全框架 │ │
│  │Operating Sys│ │Hardware Abs │ │Network Proto│ │Security │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  运行时环境  │ │  依赖管理   │ │  资源管理   │ │ 性能监控 │ │
│  │Runtime Env  │ │Dependency   │ │Resource Mgmt│ │PerfMon  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 11.1.2 模块依赖关系图

```
AI Studio 模块依赖关系：

核心模块依赖：
┌─────────────────────────────────────────────────────────────┐
│                        前端模块                              │
│                                                             │
│ ┌─────────────┐    ┌─────────────┐    ┌─────────────┐       │
│ │    App      │───▶│   Router    │───▶│   Views     │       │
│ │   主应用     │    │   路由      │    │   视图      │       │
│ └─────────────┘    └─────────────┘    └─────────────┘       │
│         │                  │                  │             │
│         ▼                  ▼                  ▼             │
│ ┌─────────────┐    ┌─────────────┐    ┌─────────────┐       │
│ │   Store     │    │ Components  │    │   Utils     │       │
│ │   状态管理   │    │   组件库     │    │   工具库     │       │
│ └─────────────┘    └─────────────┘    └─────────────┘       │
│         │                  │                  │             │
│         └──────────────────┼──────────────────┘             │
│                            ▼                                │
│                    ┌─────────────┐                          │
│                    │   Tauri     │                          │
│                    │   IPC层     │                          │
│                    └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                        后端模块                              │
│                                                             │
│ ┌─────────────┐    ┌─────────────┐    ┌─────────────┐       │
│ │  Commands   │───▶│  Services   │───▶│ Repositories│       │
│ │   命令层     │    │   服务层     │    │   数据层     │       │
│ └─────────────┘    └─────────────┘    └─────────────┘       │
│         │                  │                  │             │
│         ▼                  ▼                  ▼             │
│ ┌─────────────┐    ┌─────────────┐    ┌─────────────┐       │
│ │   Events    │    │   Models    │    │  Database   │       │
│ │   事件系统   │    │   数据模型   │    │   数据库     │       │
│ └─────────────┘    └─────────────┘    └─────────────┘       │
│         │                  │                  │             │
│         └──────────────────┼──────────────────┘             │
│                            ▼                                │
│                    ┌─────────────┐                          │
│                    │   Engines   │                          │
│                    │   引擎层     │                          │
│                    └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘

跨模块通信：
┌─────────────────────────────────────────────────────────────┐
│ 通信方式 │ 使用场景 │ 数据流向 │ 性能特点 │ 可靠性 │
├─────────────────────────────────────────────────────────────┤
│ IPC调用  │ 前后端通信 │ 双向同步 │ 中等延迟 │ 高可靠 │
│ 事件发布 │ 状态通知  │ 单向异步 │ 低延迟   │ 中可靠 │
│ 共享内存 │ 大数据传输 │ 双向同步 │ 高性能   │ 高可靠 │
│ 文件系统 │ 持久化存储 │ 双向异步 │ 低性能   │ 高可靠 │
│ 网络请求 │ 外部服务  │ 双向同步 │ 高延迟   │ 低可靠 │
└─────────────────────────────────────────────────────────────┘
```

### 11.2 **性能优化策略**

#### 11.2.1 性能优化架构

```
性能优化完整策略：

前端优化 → 后端优化 → 数据库优化 → 网络优化 → 系统优化

性能优化层次：
┌─────────────────────────────────────────────────────────────┐
│                        前端性能优化                          │
│                                                             │
│ [渲染优化] → 虚拟滚动 → 懒加载 → 组件缓存                   │
│     ↓                                                       │
│ 代码分割 → 路由懒加载 → 组件异步 → 资源预加载               │
│                                                             │
│ [状态优化] → 状态规范化 → 选择器缓存 → 更新优化             │
│     ↓                                                       │
│ 防抖节流 → 批量更新 → 内存管理 → 垃圾回收                  │
│                                                             │
│ [资源优化] → 图片压缩 → 字体优化 → 静态资源                 │
│     ↓                                                       │
│ CDN加速 → 缓存策略 → 压缩传输 → 版本控制                   │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        后端性能优化                          │
│                                                             │
│ [并发优化] → 线程池 → 异步处理 → 协程调度                   │
│     ↓                                                       │
│ 任务队列 → 负载均衡 → 资源池化 → 连接复用                  │
│                                                             │
│ [算法优化] → 时间复杂度 → 空间复杂度 → 缓存算法             │
│     ↓                                                       │
│ 索引优化 → 查询优化 → 批处理 → 流式处理                    │
│                                                             │
│ [内存优化] → 内存池 → 对象复用 → 垃圾回收                   │
│     ↓                                                       │
│ 内存映射 → 零拷贝 → 压缩存储 → 分页管理                    │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        AI推理优化                           │
│                                                             │
│ [模型优化] → 量化压缩 → 剪枝优化 → 知识蒸馏                 │
│     ↓                                                       │
│ 模型并行 → 流水线并行 → 数据并行 → 混合精度                │
│                                                             │
│ [推理优化] → 批处理推理 → 缓存预测 → 预计算                 │
│     ↓                                                       │
│ 动态批处理 → KV缓存 → 注意力优化 → 内存优化                │
│                                                             │
│ [硬件优化] → GPU加速 → 专用芯片 → 内存优化                  │
│     ↓                                                       │
│ CUDA优化 → TensorRT → ONNX Runtime → 硬件特定优化          │
└─────────────────────────────────────────────────────────────┘

性能监控指标：
┌─────────────────────────────────────────────────────────────┐
│ 指标类型 │ 监控项目 │ 目标值 │ 告警阈值 │ 优化策略 │
├─────────────────────────────────────────────────────────────┤
│ 响应时间 │ API延迟  │ <100ms │ >500ms   │ 缓存/优化 │
│ 吞吐量   │ QPS     │ >1000  │ <500     │ 扩容/优化 │
│ 资源使用 │ CPU使用率│ <70%   │ >90%     │ 负载均衡 │
│ 内存使用 │ 内存占用 │ <80%   │ >95%     │ 内存优化 │
│ 错误率   │ 失败率   │ <0.1%  │ >1%      │ 容错处理 │
│ 可用性   │ 正常运行 │ >99.9% │ <99%     │ 高可用   │
└─────────────────────────────────────────────────────────────┘
```

---

## 第十二部分：开发环境配置

### 12.1 **开发工具链配置**

#### 12.1.1 开发环境架构

```
AI Studio 开发环境完整配置：

开发工具 → 构建系统 → 测试框架 → 部署流水线 → 监控系统

开发环境层次：
┌─────────────────────────────────────────────────────────────┐
│                        IDE与编辑器                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ VS Code     │ │  WebStorm   │ │   Vim/Neovim│ │ Sublime │ │
│  │ 主要IDE     │ │  备选IDE    │ │   终端编辑   │ │ 轻量编辑 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│                                                             │
│ 扩展插件配置:                                               │
│ • Rust Analyzer (Rust语言支持)                             │
│ • Vetur/Volar (Vue.js支持)                                 │
│ • Tauri (Tauri开发支持)                                    │
│ • GitLens (Git增强)                                        │
│ • ESLint/Prettier (代码格式化)                             │
│ • Thunder Client (API测试)                                │
│ • Error Lens (错误提示增强)                                │
│ • Auto Rename Tag (标签重命名)                             │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        版本控制系统                          │
│                                                             │
│ [Git配置] → 分支策略 → 提交规范 → 合并策略                   │
│     ↓                                                       │
│ main分支 → develop分支 → feature分支 → hotfix分支           │
│                                                             │
│ [提交规范] → 类型前缀 → 作用域 → 描述信息                   │
│     ↓                                                       │
│ feat: 新功能                                               │
│ fix: 修复bug                                               │
│ docs: 文档更新                                             │
│ style: 代码格式                                            │
│ refactor: 重构                                             │
│ test: 测试相关                                             │
│ chore: 构建/工具                                           │
│                                                             │
│ [Git Hooks] → pre-commit → pre-push → commit-msg           │
│     ↓                                                       │
│ 代码检查 → 测试运行 → 格式化 → 提交验证                    │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        包管理器配置                          │
│                                                             │
│ [前端包管理] → npm/yarn/pnpm → 依赖管理 → 版本锁定          │
│     ↓                                                       │
│ package.json → package-lock.json → node_modules → scripts  │
│                                                             │
│ [后端包管理] → Cargo → 依赖管理 → 特性配置                  │
│     ↓                                                       │
│ Cargo.toml → Cargo.lock → target目录 → 构建脚本            │
│                                                             │
│ [系统依赖] → 操作系统 → 运行时 → 开发工具                   │
│     ↓                                                       │
│ Node.js → Rust → Python → Git → Docker                    │
└─────────────────────────────────────────────────────────────┘
```

#### 12.1.2 开发环境配置文件

```json
// .vscode/settings.json - VS Code配置
{
  "rust-analyzer.checkOnSave.command": "clippy",
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.procMacro.enable": true,
  "rust-analyzer.inlayHints.enable": true,

  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",

  "vue.codeActions.enabled": true,
  "vue.complete.casing.tags": "kebab",
  "vue.complete.casing.props": "camel",

  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.format.enable": true,

  "prettier.configPath": ".prettierrc.json",
  "prettier.requireConfig": true,

  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },

  "files.associations": {
    "*.rs": "rust",
    "*.vue": "vue",
    "*.toml": "toml"
  },

  "search.exclude": {
    "**/node_modules": true,
    "**/target": true,
    "**/dist": true,
    "**/.git": true
  },

  "git.autofetch": true,
  "git.enableSmartCommit": true,
  "git.confirmSync": false,

  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.defaultProfile.linux": "bash"
}
```

```json
// .vscode/launch.json - 调试配置
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Tauri Development Debug",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/src-tauri/target/debug/ai-studio",
      "args": [],
      "cwd": "${workspaceFolder}",
      "environment": [
        {
          "name": "RUST_LOG",
          "value": "debug"
        }
      ],
      "preLaunchTask": "tauri:build:debug"
    },
    {
      "name": "Frontend Debug",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:1420",
      "webRoot": "${workspaceFolder}/src",
      "sourceMaps": true,
      "preLaunchTask": "tauri:dev"
    },
    {
      "name": "Unit Tests",
      "type": "lldb",
      "request": "launch",
      "program": "${workspaceFolder}/src-tauri/target/debug/deps/ai_studio-*",
      "args": ["--nocapture"],
      "cwd": "${workspaceFolder}/src-tauri",
      "preLaunchTask": "cargo:test:build"
    }
  ]
}
```

```json
// .vscode/tasks.json - 任务配置
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "tauri:dev",
      "type": "shell",
      "command": "npm",
      "args": ["run", "tauri", "dev"],
      "group": "build",
      "isBackground": true,
      "problemMatcher": {
        "owner": "rust",
        "fileLocation": ["relative", "${workspaceFolder}/src-tauri"],
        "pattern": {
          "regexp": "^(.*):(\\d+):(\\d+):\\s+(\\d+):(\\d+)\\s+(warning|error):\\s+(.*)$",
          "file": 1,
          "line": 2,
          "column": 3,
          "endLine": 4,
          "endColumn": 5,
          "severity": 6,
          "message": 7
        }
      }
    },
    {
      "label": "tauri:build",
      "type": "shell",
      "command": "npm",
      "args": ["run", "tauri", "build"],
      "group": "build",
      "problemMatcher": ["$rustc"]
    },
    {
      "label": "tauri:build:debug",
      "type": "shell",
      "command": "cargo",
      "args": ["build"],
      "options": {
        "cwd": "${workspaceFolder}/src-tauri"
      },
      "group": "build",
      "problemMatcher": ["$rustc"]
    },
    {
      "label": "cargo:test",
      "type": "shell",
      "command": "cargo",
      "args": ["test"],
      "options": {
        "cwd": "${workspaceFolder}/src-tauri"
      },
      "group": "test",
      "problemMatcher": ["$rustc"]
    },
    {
      "label": "cargo:test:build",
      "type": "shell",
      "command": "cargo",
      "args": ["test", "--no-run"],
      "options": {
        "cwd": "${workspaceFolder}/src-tauri"
      },
      "group": "build",
      "problemMatcher": ["$rustc"]
    },
    {
      "label": "frontend:dev",
      "type": "shell",
      "command": "npm",
      "args": ["run", "dev"],
      "group": "build",
      "isBackground": true
    },
    {
      "label": "frontend:build",
      "type": "shell",
      "command": "npm",
      "args": ["run", "build"],
      "group": "build"
    },
    {
      "label": "frontend:test",
      "type": "shell",
      "command": "npm",
      "args": ["run", "test"],
      "group": "test"
    },
    {
      "label": "lint:fix",
      "type": "shell",
      "command": "npm",
      "args": ["run", "lint:fix"],
      "group": "build"
    },
    {
      "label": "format:all",
      "type": "shell",
      "command": "npm",
      "args": ["run", "format"],
      "group": "build"
    }
  ]
}
```

### 12.2 **构建与部署配置**

#### 12.2.1 构建系统配置

```yaml
# .github/workflows/ci.yml - CI/CD配置
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CARGO_TERM_COLOR: always
  NODE_VERSION: '18'
  RUST_VERSION: '1.70'

jobs:
  # 代码质量检查
  lint-and-format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_VERSION }}
          components: rustfmt, clippy
          override: true

      - name: Install dependencies
        run: npm ci

      - name: Lint frontend
        run: npm run lint

      - name: Format check frontend
        run: npm run format:check

      - name: Lint backend
        run: cargo clippy --all-targets --all-features -- -D warnings
        working-directory: src-tauri

      - name: Format check backend
        run: cargo fmt --all -- --check
        working-directory: src-tauri

  # 单元测试
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_VERSION }}
          override: true

      - name: Install system dependencies (Ubuntu)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install dependencies
        run: npm ci

      - name: Run frontend tests
        run: npm run test:unit

      - name: Run backend tests
        run: cargo test --verbose
        working-directory: src-tauri

      - name: Run integration tests
        run: npm run test:e2e
        if: matrix.os == 'ubuntu-latest'

  # 构建应用
  build:
    needs: [lint-and-format, test]
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_VERSION }}
          override: true

      - name: Install system dependencies (Ubuntu)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run tauri build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ai-studio-${{ matrix.os }}
          path: |
            src-tauri/target/release/bundle/
            !src-tauri/target/release/bundle/**/*.deb
            !src-tauri/target/release/bundle/**/*.rpm

  # 发布版本
  release:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Download artifacts
        uses: actions/download-artifact@v3

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ github.run_number }}
          name: Release v${{ github.run_number }}
          draft: false
          prerelease: false
          files: |
            ai-studio-ubuntu-latest/**/*
            ai-studio-windows-latest/**/*
            ai-studio-macos-latest/**/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

---

## 第十三部分：部署与运维

### 13.1 **部署策略设计**

#### 13.1.1 多平台部署架构

```
AI Studio 多平台部署策略：

开发构建 → 测试验证 → 打包分发 → 自动更新 → 监控运维

部署流程架构：
┌─────────────────────────────────────────────────────────────┐
│                        构建阶段                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ Windows构建  │ │  macOS构建  │ │  Linux构建  │ │ 交叉编译 │ │
│  │Windows Build│ │ macOS Build │ │Linux Build  │ │CrossComp│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│                                                             │
│ 构建产物:                                                   │
│ • Windows: .msi, .exe                                       │
│ • macOS: .dmg, .app                                         │
│ • Linux: .deb, .rpm, .AppImage                             │
│ • 便携版: .zip, .tar.gz                                     │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        测试阶段                              │
│                                                             │
│ [自动化测试] → 单元测试 → 集成测试 → E2E测试                │
│     ↓                                                       │
│ 功能测试 → 性能测试 → 兼容性测试 → 安全测试                │
│                                                             │
│ [手动测试] → 用户体验 → 界面测试 → 场景测试                 │
│     ↓                                                       │
│ 回归测试 → 压力测试 → 边界测试 → 异常测试                  │
│                                                             │
│ [质量门禁] → 代码覆盖率 → 性能基准 → 安全扫描               │
│     ↓                                                       │
│ 覆盖率>80% → 响应时间<100ms → 无高危漏洞 → 通过发布        │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        分发阶段                              │
│                                                             │
│ [官方渠道] → GitHub Releases → 官方网站 → 应用商店          │
│     ↓                                                       │
│ 版本管理 → 发布说明 → 下载统计 → 用户反馈                  │
│                                                             │
│ [第三方渠道] → 软件管家 → 包管理器 → 镜像站点               │
│     ↓                                                       │
│ Chocolatey → Homebrew → Snap → Flatpak                     │
│                                                             │
│ [企业分发] → 内部部署 → 定制版本 → 批量授权                 │
│     ↓                                                       │
│ 私有仓库 → 配置管理 → 权限控制 → 审计日志                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        更新阶段                              │
│ • 自动更新检测 • 增量更新 • 回滚机制 • 更新通知             │
│ • 静默更新 • 用户确认 • 更新日志 • 兼容性检查               │
└─────────────────────────────────────────────────────────────┘

部署环境配置：
┌─────────────────────────────────────────────────────────────┐
│ 环境类型 │ 用途 │ 配置 │ 数据 │ 监控 │ 备份 │
├─────────────────────────────────────────────────────────────┤
│ 开发环境 │ 开发调试 │ 开发配置 │ 测试数据 │ 基础监控 │ 无 │
│ 测试环境 │ 功能测试 │ 测试配置 │ 模拟数据 │ 详细监控 │ 定期 │
│ 预发环境 │ 发布验证 │ 生产配置 │ 脱敏数据 │ 生产监控 │ 实时 │
│ 生产环境 │ 正式运行 │ 生产配置 │ 真实数据 │ 全面监控 │ 实时 │
│ 灾备环境 │ 容灾恢复 │ 生产配置 │ 备份数据 │ 基础监控 │ 同步 │
└─────────────────────────────────────────────────────────────┘
```

#### 13.1.2 自动更新机制

```rust
// src/services/updater_service.rs - 自动更新服务
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tokio::time::{Duration, interval};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateInfo {
    pub version: String,
    pub release_date: chrono::DateTime<chrono::Utc>,
    pub download_url: String,
    pub file_size: u64,
    pub checksum: String,
    pub release_notes: String,
    pub is_critical: bool,
    pub min_version: Option<String>,
    pub platforms: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateConfig {
    pub check_interval: Duration,
    pub auto_download: bool,
    pub auto_install: bool,
    pub notify_user: bool,
    pub check_on_startup: bool,
    pub use_beta_channel: bool,
    pub update_server_url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdateStatus {
    CheckingForUpdates,
    UpdateAvailable(UpdateInfo),
    Downloading { progress: f32, speed: u64 },
    Downloaded { file_path: PathBuf },
    Installing,
    Installed,
    Failed { error: String },
    UpToDate,
}

pub struct UpdaterService {
    config: UpdateConfig,
    current_version: String,
    status: UpdateStatus,
    download_manager: Arc<DownloadManager>,
    event_emitter: Arc<EventEmitter>,
}

impl UpdaterService {
    pub fn new(
        config: UpdateConfig,
        current_version: String,
        download_manager: Arc<DownloadManager>,
        event_emitter: Arc<EventEmitter>,
    ) -> Self {
        Self {
            config,
            current_version,
            status: UpdateStatus::UpToDate,
            download_manager,
            event_emitter,
        }
    }

    // 启动更新服务
    pub async fn start(&self) {
        // 启动时检查更新
        if self.config.check_on_startup {
            self.check_for_updates().await;
        }

        // 定期检查更新
        let mut interval = interval(self.config.check_interval);
        let service = self.clone();

        tokio::spawn(async move {
            loop {
                interval.tick().await;
                service.check_for_updates().await;
            }
        });
    }

    // 检查更新
    pub async fn check_for_updates(&self) -> Result<UpdateStatus, UpdateError> {
        self.status = UpdateStatus::CheckingForUpdates;
        self.emit_status_update().await;

        // 获取更新信息
        let update_info = self.fetch_update_info().await?;

        // 比较版本
        if self.is_newer_version(&update_info.version) {
            self.status = UpdateStatus::UpdateAvailable(update_info.clone());
            self.emit_status_update().await;

            // 自动下载
            if self.config.auto_download {
                self.download_update(&update_info).await?;
            }

            Ok(UpdateStatus::UpdateAvailable(update_info))
        } else {
            self.status = UpdateStatus::UpToDate;
            self.emit_status_update().await;
            Ok(UpdateStatus::UpToDate)
        }
    }

    // 下载更新
    pub async fn download_update(&self, update_info: &UpdateInfo) -> Result<PathBuf, UpdateError> {
        let download_path = self.get_download_path(&update_info.version);

        // 检查是否已下载
        if download_path.exists() && self.verify_checksum(&download_path, &update_info.checksum).await? {
            self.status = UpdateStatus::Downloaded { file_path: download_path.clone() };
            self.emit_status_update().await;
            return Ok(download_path);
        }

        // 开始下载
        let progress_callback = {
            let service = self.clone();
            move |progress: f32, speed: u64| {
                let service = service.clone();
                tokio::spawn(async move {
                    service.status = UpdateStatus::Downloading { progress, speed };
                    service.emit_status_update().await;
                });
            }
        };

        self.download_manager.download_with_progress(
            &update_info.download_url,
            &download_path,
            progress_callback,
        ).await?;

        // 验证下载文件
        if !self.verify_checksum(&download_path, &update_info.checksum).await? {
            return Err(UpdateError::ChecksumMismatch);
        }

        self.status = UpdateStatus::Downloaded { file_path: download_path.clone() };
        self.emit_status_update().await;

        // 自动安装
        if self.config.auto_install {
            self.install_update(&download_path).await?;
        }

        Ok(download_path)
    }

    // 安装更新
    pub async fn install_update(&self, file_path: &PathBuf) -> Result<(), UpdateError> {
        self.status = UpdateStatus::Installing;
        self.emit_status_update().await;

        // 创建备份
        self.create_backup().await?;

        // 执行安装
        match self.get_platform() {
            Platform::Windows => self.install_windows_update(file_path).await?,
            Platform::MacOS => self.install_macos_update(file_path).await?,
            Platform::Linux => self.install_linux_update(file_path).await?,
        }

        self.status = UpdateStatus::Installed;
        self.emit_status_update().await;

        // 通知用户重启
        self.notify_restart_required().await;

        Ok(())
    }

    // 回滚更新
    pub async fn rollback_update(&self) -> Result<(), UpdateError> {
        let backup_path = self.get_backup_path();

        if !backup_path.exists() {
            return Err(UpdateError::NoBackupFound);
        }

        // 恢复备份
        self.restore_backup(&backup_path).await?;

        // 通知用户
        self.notify_rollback_complete().await;

        Ok(())
    }

    // 内部方法
    async fn fetch_update_info(&self) -> Result<UpdateInfo, UpdateError> {
        let url = format!("{}/api/v1/updates/check", self.config.update_server_url);
        let client = reqwest::Client::new();

        let response = client
            .get(&url)
            .query(&[
                ("current_version", &self.current_version),
                ("platform", &self.get_platform().to_string()),
                ("channel", if self.config.use_beta_channel { "beta" } else { "stable" }),
            ])
            .send()
            .await?;

        if response.status().is_success() {
            let update_info: UpdateInfo = response.json().await?;
            Ok(update_info)
        } else {
            Err(UpdateError::ServerError(response.status().to_string()))
        }
    }

    fn is_newer_version(&self, new_version: &str) -> bool {
        // 简单的版本比较，实际应该使用semver
        version_compare::compare(new_version, &self.current_version)
            .map(|cmp| cmp == version_compare::Cmp::Gt)
            .unwrap_or(false)
    }

    async fn verify_checksum(&self, file_path: &PathBuf, expected_checksum: &str) -> Result<bool, UpdateError> {
        use sha2::{Sha256, Digest};
        use tokio::fs::File;
        use tokio::io::AsyncReadExt;

        let mut file = File::open(file_path).await?;
        let mut hasher = Sha256::new();
        let mut buffer = [0; 8192];

        loop {
            let bytes_read = file.read(&mut buffer).await?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }

        let calculated_checksum = format!("{:x}", hasher.finalize());
        Ok(calculated_checksum == expected_checksum)
    }

    async fn emit_status_update(&self) {
        let _ = self.event_emitter.emit("update_status_changed", &self.status);
    }
}

#[derive(Debug, thiserror::Error)]
pub enum UpdateError {
    #[error("Network error: {0}")]
    NetworkError(#[from] reqwest::Error),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Server error: {0}")]
    ServerError(String),

    #[error("Checksum mismatch")]
    ChecksumMismatch,

    #[error("No backup found")]
    NoBackupFound,

    #[error("Installation failed: {0}")]
    InstallationFailed(String),
}
```

---

## 第十四部分：监控与日志

### 14.1 **监控系统设计**

#### 14.1.1 监控架构总览

```
AI Studio 监控系统架构：

数据采集 → 数据处理 → 存储分析 → 可视化展示 → 告警通知

监控层次架构：
┌─────────────────────────────────────────────────────────────┐
│                        应用监控层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  性能监控   │ │  错误监控   │ │  用户行为   │ │ 业务指标 │ │
│  │Performance  │ │Error Track  │ │User Behavior│ │Business │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│                                                             │
│ 监控指标:                                                   │
│ • 响应时间 • 吞吐量 • 错误率 • 可用性                       │
│ • 内存使用 • CPU使用 • 磁盘I/O • 网络I/O                    │
│ • 用户活跃度 • 功能使用率 • 转化率 • 留存率                 │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        系统监控层                            │
│                                                             │
│ [资源监控] → CPU监控 → 内存监控 → 磁盘监控                  │
│     ↓                                                       │
│ 使用率统计 → 负载分析 → 容量规划 → 性能优化                │
│                                                             │
│ [服务监控] → 进程监控 → 端口监控 → 服务状态                 │
│     ↓                                                       │
│ 健康检查 → 依赖检测 → 故障恢复 → 服务发现                  │
│                                                             │
│ [网络监控] → 连接监控 → 带宽监控 → 延迟监控                 │
│     ↓                                                       │
│ 流量分析 → 质量评估 → 异常检测 → 优化建议                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        基础设施监控层                        │
│ • 硬件监控 • 操作系统监控 • 虚拟化监控 • 容器监控           │
│ • 存储监控 • 网络设备监控 • 安全监控 • 环境监控             │
└─────────────────────────────────────────────────────────────┘

监控数据流：
┌─────────────────────────────────────────────────────────────┐
│ 数据源 → 采集器 → 处理器 → 存储 → 分析 → 展示 → 告警       │
│                                                             │
│ [数据源类型]                                                │
│ • 应用日志 • 系统指标 • 业务事件 • 用户行为                 │
│ • 错误信息 • 性能数据 • 安全事件 • 外部API                  │
│                                                             │
│ [采集方式]                                                  │
│ • 主动拉取 • 被动推送 • 事件驱动 • 定时采集                 │
│ • 实时流式 • 批量处理 • 增量同步 • 全量备份                 │
│                                                             │
│ [处理流程]                                                  │
│ • 数据清洗 • 格式转换 • 聚合计算 • 异常检测                 │
│ • 趋势分析 • 关联分析 • 预测分析 • 智能告警                 │
└─────────────────────────────────────────────────────────────┘
```

#### 14.1.2 性能监控实现

```rust
// src/monitoring/performance_monitor.rs - 性能监控实现
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub cpu_usage: f64,
    pub memory_usage: MemoryUsage,
    pub disk_usage: DiskUsage,
    pub network_usage: NetworkUsage,
    pub application_metrics: ApplicationMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryUsage {
    pub total: u64,
    pub used: u64,
    pub available: u64,
    pub usage_percentage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskUsage {
    pub total: u64,
    pub used: u64,
    pub available: u64,
    pub usage_percentage: f64,
    pub read_bytes_per_sec: u64,
    pub write_bytes_per_sec: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkUsage {
    pub bytes_sent_per_sec: u64,
    pub bytes_received_per_sec: u64,
    pub packets_sent_per_sec: u64,
    pub packets_received_per_sec: u64,
    pub connection_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApplicationMetrics {
    pub active_sessions: u32,
    pub total_requests: u64,
    pub requests_per_second: f64,
    pub average_response_time: Duration,
    pub error_rate: f64,
    pub ai_inference_metrics: AIInferenceMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIInferenceMetrics {
    pub total_inferences: u64,
    pub inferences_per_second: f64,
    pub average_inference_time: Duration,
    pub model_memory_usage: u64,
    pub gpu_utilization: Option<f64>,
    pub queue_length: u32,
}

pub struct PerformanceMonitor {
    metrics_history: Arc<RwLock<Vec<PerformanceMetrics>>>,
    collection_interval: Duration,
    max_history_size: usize,
    system_info: Arc<SystemInfo>,
    event_emitter: Arc<EventEmitter>,
}

impl PerformanceMonitor {
    pub fn new(
        collection_interval: Duration,
        max_history_size: usize,
        event_emitter: Arc<EventEmitter>,
    ) -> Self {
        Self {
            metrics_history: Arc::new(RwLock::new(Vec::new())),
            collection_interval,
            max_history_size,
            system_info: Arc::new(SystemInfo::new()),
            event_emitter,
        }
    }

    // 启动性能监控
    pub async fn start(&self) {
        let monitor = self.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(monitor.collection_interval);

            loop {
                interval.tick().await;

                match monitor.collect_metrics().await {
                    Ok(metrics) => {
                        monitor.store_metrics(metrics.clone()).await;
                        monitor.analyze_metrics(&metrics).await;

                        // 发送监控事件
                        let _ = monitor.event_emitter.emit("performance_metrics", &metrics);
                    }
                    Err(e) => {
                        log::error!("Failed to collect performance metrics: {}", e);
                    }
                }
            }
        });
    }

    // 收集性能指标
    async fn collect_metrics(&self) -> Result<PerformanceMetrics, MonitoringError> {
        let timestamp = chrono::Utc::now();

        // 收集系统指标
        let cpu_usage = self.system_info.get_cpu_usage().await?;
        let memory_usage = self.system_info.get_memory_usage().await?;
        let disk_usage = self.system_info.get_disk_usage().await?;
        let network_usage = self.system_info.get_network_usage().await?;

        // 收集应用指标
        let application_metrics = self.collect_application_metrics().await?;

        Ok(PerformanceMetrics {
            timestamp,
            cpu_usage,
            memory_usage,
            disk_usage,
            network_usage,
            application_metrics,
        })
    }

    // 收集应用指标
    async fn collect_application_metrics(&self) -> Result<ApplicationMetrics, MonitoringError> {
        // 这里需要从应用的各个服务中收集指标
        // 示例实现
        Ok(ApplicationMetrics {
            active_sessions: 0, // 从会话管理器获取
            total_requests: 0,  // 从请求计数器获取
            requests_per_second: 0.0,
            average_response_time: Duration::from_millis(0),
            error_rate: 0.0,
            ai_inference_metrics: AIInferenceMetrics {
                total_inferences: 0,
                inferences_per_second: 0.0,
                average_inference_time: Duration::from_millis(0),
                model_memory_usage: 0,
                gpu_utilization: None,
                queue_length: 0,
            },
        })
    }

    // 存储指标
    async fn store_metrics(&self, metrics: PerformanceMetrics) {
        let mut history = self.metrics_history.write().await;

        history.push(metrics);

        // 保持历史记录大小限制
        if history.len() > self.max_history_size {
            history.remove(0);
        }
    }

    // 分析指标
    async fn analyze_metrics(&self, metrics: &PerformanceMetrics) {
        // CPU使用率告警
        if metrics.cpu_usage > 90.0 {
            self.emit_alert(AlertLevel::Critical, "CPU usage is critically high", metrics).await;
        } else if metrics.cpu_usage > 80.0 {
            self.emit_alert(AlertLevel::Warning, "CPU usage is high", metrics).await;
        }

        // 内存使用率告警
        if metrics.memory_usage.usage_percentage > 95.0 {
            self.emit_alert(AlertLevel::Critical, "Memory usage is critically high", metrics).await;
        } else if metrics.memory_usage.usage_percentage > 85.0 {
            self.emit_alert(AlertLevel::Warning, "Memory usage is high", metrics).await;
        }

        // 磁盘使用率告警
        if metrics.disk_usage.usage_percentage > 95.0 {
            self.emit_alert(AlertLevel::Critical, "Disk usage is critically high", metrics).await;
        } else if metrics.disk_usage.usage_percentage > 90.0 {
            self.emit_alert(AlertLevel::Warning, "Disk usage is high", metrics).await;
        }

        // 应用性能告警
        if metrics.application_metrics.error_rate > 5.0 {
            self.emit_alert(AlertLevel::Critical, "Application error rate is high", metrics).await;
        }

        if metrics.application_metrics.average_response_time > Duration::from_millis(1000) {
            self.emit_alert(AlertLevel::Warning, "Application response time is slow", metrics).await;
        }
    }

    // 发送告警
    async fn emit_alert(&self, level: AlertLevel, message: &str, metrics: &PerformanceMetrics) {
        let alert = Alert {
            level,
            message: message.to_string(),
            timestamp: chrono::Utc::now(),
            metrics: metrics.clone(),
        };

        let _ = self.event_emitter.emit("performance_alert", &alert);

        // 记录告警日志
        match level {
            AlertLevel::Critical => log::error!("CRITICAL ALERT: {}", message),
            AlertLevel::Warning => log::warn!("WARNING ALERT: {}", message),
            AlertLevel::Info => log::info!("INFO ALERT: {}", message),
        }
    }

    // 获取历史指标
    pub async fn get_metrics_history(&self, duration: Duration) -> Vec<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        let cutoff_time = chrono::Utc::now() - chrono::Duration::from_std(duration).unwrap();

        history
            .iter()
            .filter(|metrics| metrics.timestamp > cutoff_time)
            .cloned()
            .collect()
    }

    // 获取当前指标
    pub async fn get_current_metrics(&self) -> Option<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        history.last().cloned()
    }

    // 生成性能报告
    pub async fn generate_performance_report(&self, duration: Duration) -> PerformanceReport {
        let metrics = self.get_metrics_history(duration).await;

        if metrics.is_empty() {
            return PerformanceReport::default();
        }

        let avg_cpu = metrics.iter().map(|m| m.cpu_usage).sum::<f64>() / metrics.len() as f64;
        let avg_memory = metrics.iter().map(|m| m.memory_usage.usage_percentage).sum::<f64>() / metrics.len() as f64;
        let avg_response_time = Duration::from_nanos(
            metrics.iter().map(|m| m.application_metrics.average_response_time.as_nanos()).sum::<u128>() / metrics.len() as u128
        );

        let max_cpu = metrics.iter().map(|m| m.cpu_usage).fold(0.0, f64::max);
        let max_memory = metrics.iter().map(|m| m.memory_usage.usage_percentage).fold(0.0, f64::max);

        PerformanceReport {
            period: duration,
            sample_count: metrics.len(),
            average_cpu_usage: avg_cpu,
            average_memory_usage: avg_memory,
            average_response_time: avg_response_time,
            peak_cpu_usage: max_cpu,
            peak_memory_usage: max_memory,
            total_requests: metrics.last().unwrap().application_metrics.total_requests,
            total_errors: 0, // 需要从错误统计中获取
            uptime_percentage: 99.9, // 需要从可用性统计中获取
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertLevel {
    Critical,
    Warning,
    Info,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    pub level: AlertLevel,
    pub message: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub metrics: PerformanceMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceReport {
    pub period: Duration,
    pub sample_count: usize,
    pub average_cpu_usage: f64,
    pub average_memory_usage: f64,
    pub average_response_time: Duration,
    pub peak_cpu_usage: f64,
    pub peak_memory_usage: f64,
    pub total_requests: u64,
    pub total_errors: u64,
    pub uptime_percentage: f64,
}

impl Default for PerformanceReport {
    fn default() -> Self {
        Self {
            period: Duration::from_secs(0),
            sample_count: 0,
            average_cpu_usage: 0.0,
            average_memory_usage: 0.0,
            average_response_time: Duration::from_secs(0),
            peak_cpu_usage: 0.0,
            peak_memory_usage: 0.0,
            total_requests: 0,
            total_errors: 0,
            uptime_percentage: 0.0,
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum MonitoringError {
    #[error("System information error: {0}")]
    SystemInfoError(String),

    #[error("Collection error: {0}")]
    CollectionError(String),

    #[error("Storage error: {0}")]
    StorageError(String),
}
```

---

## 第十五部分：安全与权限

### 15.1 **安全架构设计**

#### 15.1.1 安全防护体系

```
AI Studio 安全防护完整体系：

身份认证 → 权限控制 → 数据加密 → 安全审计 → 威胁防护

安全防护层次：
┌─────────────────────────────────────────────────────────────┐
│                        应用安全层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  身份认证   │ │  权限控制   │ │  会话管理   │ │ 输入验证 │ │
│  │Authentication│ │Authorization│ │Session Mgmt │ │InputVal │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│                                                             │
│ 安全机制:                                                   │
│ • JWT令牌认证 • RBAC权限模型 • 会话超时 • XSS防护           │
│ • 密码策略 • 多因素认证 • 单点登录 • CSRF防护               │
│ • SQL注入防护 • 文件上传安全 • API限流 • 安全头设置         │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        数据安全层                            │
│                                                             │
│ [数据加密] → 传输加密 → 存储加密 → 密钥管理                 │
│     ↓                                                       │
│ TLS/SSL → AES-256 → RSA-2048 → 密钥轮换                    │
│                                                             │
│ [数据保护] → 数据分类 → 访问控制 → 数据脱敏                 │
│     ↓                                                       │
│ 敏感数据标识 → 最小权限原则 → 匿名化处理 → 备份加密         │
│                                                             │
│ [隐私保护] → 用户同意 → 数据最小化 → 删除权利               │
│     ↓                                                       │
│ GDPR合规 → 隐私政策 → 数据审计 → 违规通知                  │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        网络安全层                            │
│                                                             │
│ [网络防护] → 防火墙 → DDoS防护 → 入侵检测                  │
│     ↓                                                       │
│ 端口扫描防护 → 流量分析 → 异常检测 → 自动阻断               │
│                                                             │
│ [通信安全] → 证书管理 → 协议安全 → 端到端加密               │
│     ↓                                                       │
│ SSL/TLS配置 → 证书验证 → 密钥交换 → 完整性校验             │
│                                                             │
│ [API安全] → 接口认证 → 请求签名 → 频率限制                  │
│     ↓                                                       │
│ OAuth2.0 → HMAC签名 → 令牌桶算法 → 黑白名单                │
└─────────────────────────────────────────────────────────────┘
        ↓
┌─────────────────────────────────────────────────────────────┐
│                        系统安全层                            │
│ • 操作系统加固 • 容器安全 • 依赖安全 • 代码安全             │
│ • 漏洞扫描 • 安全更新 • 配置管理 • 监控告警                 │
└─────────────────────────────────────────────────────────────┘

安全威胁模型：
┌─────────────────────────────────────────────────────────────┐
│ 威胁类型 │ 攻击向量 │ 影响程度 │ 防护措施 │ 检测方法 │
├─────────────────────────────────────────────────────────────┤
│ 身份伪造 │ 弱密码/社工 │ 高 │ 强密码+2FA │ 异常登录 │
│ 权限提升 │ 漏洞利用 │ 高 │ 最小权限 │ 行为分析 │
│ 数据泄露 │ SQL注入/XSS │ 极高 │ 输入验证 │ 数据监控 │
│ 拒绝服务 │ DDoS攻击 │ 中 │ 限流防护 │ 流量监控 │
│ 恶意代码 │ 文件上传 │ 高 │ 文件扫描 │ 病毒检测 │
│ 中间人攻击│ 网络窃听 │ 高 │ 端到端加密│ 证书验证 │
└─────────────────────────────────────────────────────────────┘
```

#### 15.1.2 权限管理系统

```rust
// src/security/permission_manager.rs - 权限管理系统
use std::collections::{HashMap, HashSet};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum Permission {
    // 聊天权限
    ChatCreate,
    ChatRead,
    ChatUpdate,
    ChatDelete,
    ChatExport,

    // 知识库权限
    KnowledgeCreate,
    KnowledgeRead,
    KnowledgeUpdate,
    KnowledgeDelete,
    KnowledgeShare,

    // 模型权限
    ModelView,
    ModelDownload,
    ModelManage,
    ModelConfigure,

    // 系统权限
    SystemSettings,
    SystemMonitor,
    SystemBackup,
    SystemUpdate,

    // 用户权限
    UserManage,
    UserView,
    UserCreate,
    UserDelete,

    // 插件权限
    PluginInstall,
    PluginConfigure,
    PluginManage,

    // 网络权限
    NetworkShare,
    NetworkConnect,
    NetworkManage,

    // 数据权限
    DataExport,
    DataImport,
    DataBackup,
    DataRestore,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Role {
    Guest,
    User,
    Premium,
    Admin,
    SuperAdmin,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionSet {
    pub permissions: HashSet<Permission>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPermissions {
    pub user_id: String,
    pub role: Role,
    pub permissions: PermissionSet,
    pub custom_permissions: HashSet<Permission>,
    pub denied_permissions: HashSet<Permission>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
}

pub struct PermissionManager {
    role_permissions: HashMap<Role, PermissionSet>,
    user_permissions: HashMap<String, UserPermissions>,
    permission_cache: HashMap<String, (PermissionSet, chrono::DateTime<chrono::Utc>)>,
}

impl PermissionManager {
    pub fn new() -> Self {
        let mut manager = Self {
            role_permissions: HashMap::new(),
            user_permissions: HashMap::new(),
            permission_cache: HashMap::new(),
        };

        manager.initialize_default_roles();
        manager
    }

    // 初始化默认角色权限
    fn initialize_default_roles(&mut self) {
        // 游客权限
        let guest_permissions = PermissionSet {
            permissions: [
                Permission::ChatRead,
                Permission::KnowledgeRead,
                Permission::ModelView,
            ].iter().cloned().collect(),
        };

        // 普通用户权限
        let user_permissions = PermissionSet {
            permissions: [
                Permission::ChatCreate,
                Permission::ChatRead,
                Permission::ChatUpdate,
                Permission::ChatDelete,
                Permission::ChatExport,
                Permission::KnowledgeCreate,
                Permission::KnowledgeRead,
                Permission::KnowledgeUpdate,
                Permission::KnowledgeDelete,
                Permission::ModelView,
                Permission::ModelDownload,
                Permission::PluginInstall,
                Permission::PluginConfigure,
                Permission::DataExport,
                Permission::DataImport,
            ].iter().cloned().collect(),
        };

        // 高级用户权限
        let premium_permissions = PermissionSet {
            permissions: user_permissions.permissions.iter().cloned()
                .chain([
                    Permission::KnowledgeShare,
                    Permission::ModelManage,
                    Permission::ModelConfigure,
                    Permission::NetworkShare,
                    Permission::NetworkConnect,
                    Permission::PluginManage,
                    Permission::DataBackup,
                    Permission::DataRestore,
                ].iter().cloned())
                .collect(),
        };

        // 管理员权限
        let admin_permissions = PermissionSet {
            permissions: premium_permissions.permissions.iter().cloned()
                .chain([
                    Permission::SystemSettings,
                    Permission::SystemMonitor,
                    Permission::SystemBackup,
                    Permission::UserManage,
                    Permission::UserView,
                    Permission::UserCreate,
                    Permission::NetworkManage,
                ].iter().cloned())
                .collect(),
        };

        // 超级管理员权限
        let super_admin_permissions = PermissionSet {
            permissions: admin_permissions.permissions.iter().cloned()
                .chain([
                    Permission::SystemUpdate,
                    Permission::UserDelete,
                ].iter().cloned())
                .collect(),
        };

        self.role_permissions.insert(Role::Guest, guest_permissions);
        self.role_permissions.insert(Role::User, user_permissions);
        self.role_permissions.insert(Role::Premium, premium_permissions);
        self.role_permissions.insert(Role::Admin, admin_permissions);
        self.role_permissions.insert(Role::SuperAdmin, super_admin_permissions);
    }

    // 检查用户权限
    pub fn check_permission(&self, user_id: &str, permission: &Permission) -> bool {
        // 从缓存获取权限
        if let Some((cached_permissions, cached_at)) = self.permission_cache.get(user_id) {
            // 检查缓存是否过期（5分钟）
            if chrono::Utc::now().signed_duration_since(*cached_at).num_minutes() < 5 {
                return self.has_permission(cached_permissions, permission);
            }
        }

        // 获取用户权限
        let user_permissions = self.get_user_permissions(user_id);
        let effective_permissions = self.calculate_effective_permissions(&user_permissions);

        // 更新缓存
        self.permission_cache.insert(
            user_id.to_string(),
            (effective_permissions.clone(), chrono::Utc::now()),
        );

        self.has_permission(&effective_permissions, permission)
    }

    // 获取用户权限
    fn get_user_permissions(&self, user_id: &str) -> UserPermissions {
        self.user_permissions.get(user_id)
            .cloned()
            .unwrap_or_else(|| UserPermissions {
                user_id: user_id.to_string(),
                role: Role::Guest,
                permissions: PermissionSet { permissions: HashSet::new() },
                custom_permissions: HashSet::new(),
                denied_permissions: HashSet::new(),
                expires_at: None,
            })
    }

    // 计算有效权限
    fn calculate_effective_permissions(&self, user_permissions: &UserPermissions) -> PermissionSet {
        // 检查权限是否过期
        if let Some(expires_at) = user_permissions.expires_at {
            if chrono::Utc::now() > expires_at {
                return PermissionSet { permissions: HashSet::new() };
            }
        }

        // 获取角色权限
        let role_permissions = self.role_permissions.get(&user_permissions.role)
            .map(|p| &p.permissions)
            .unwrap_or(&HashSet::new());

        // 合并权限：角色权限 + 自定义权限 + 用户权限 - 拒绝权限
        let mut effective_permissions = role_permissions.clone();
        effective_permissions.extend(user_permissions.custom_permissions.iter().cloned());
        effective_permissions.extend(user_permissions.permissions.permissions.iter().cloned());

        // 移除被拒绝的权限
        for denied_permission in &user_permissions.denied_permissions {
            effective_permissions.remove(denied_permission);
        }

        PermissionSet { permissions: effective_permissions }
    }

    // 检查权限集合中是否包含指定权限
    fn has_permission(&self, permission_set: &PermissionSet, permission: &Permission) -> bool {
        permission_set.permissions.contains(permission)
    }

    // 设置用户角色
    pub fn set_user_role(&mut self, user_id: &str, role: Role) {
        let user_permissions = self.user_permissions.entry(user_id.to_string())
            .or_insert_with(|| UserPermissions {
                user_id: user_id.to_string(),
                role: Role::Guest,
                permissions: PermissionSet { permissions: HashSet::new() },
                custom_permissions: HashSet::new(),
                denied_permissions: HashSet::new(),
                expires_at: None,
            });

        user_permissions.role = role;

        // 清除缓存
        self.permission_cache.remove(user_id);
    }

    // 授予用户权限
    pub fn grant_permission(&mut self, user_id: &str, permission: Permission) {
        let user_permissions = self.user_permissions.entry(user_id.to_string())
            .or_insert_with(|| UserPermissions {
                user_id: user_id.to_string(),
                role: Role::Guest,
                permissions: PermissionSet { permissions: HashSet::new() },
                custom_permissions: HashSet::new(),
                denied_permissions: HashSet::new(),
                expires_at: None,
            });

        user_permissions.custom_permissions.insert(permission);
        user_permissions.denied_permissions.remove(&permission);

        // 清除缓存
        self.permission_cache.remove(user_id);
    }

    // 撤销用户权限
    pub fn revoke_permission(&mut self, user_id: &str, permission: Permission) {
        let user_permissions = self.user_permissions.entry(user_id.to_string())
            .or_insert_with(|| UserPermissions {
                user_id: user_id.to_string(),
                role: Role::Guest,
                permissions: PermissionSet { permissions: HashSet::new() },
                custom_permissions: HashSet::new(),
                denied_permissions: HashSet::new(),
                expires_at: None,
            });

        user_permissions.custom_permissions.remove(&permission);
        user_permissions.denied_permissions.insert(permission);

        // 清除缓存
        self.permission_cache.remove(user_id);
    }

    // 获取用户所有权限
    pub fn get_user_all_permissions(&self, user_id: &str) -> PermissionSet {
        let user_permissions = self.get_user_permissions(user_id);
        self.calculate_effective_permissions(&user_permissions)
    }

    // 检查多个权限
    pub fn check_permissions(&self, user_id: &str, permissions: &[Permission]) -> bool {
        permissions.iter().all(|permission| self.check_permission(user_id, permission))
    }

    // 检查任一权限
    pub fn check_any_permission(&self, user_id: &str, permissions: &[Permission]) -> bool {
        permissions.iter().any(|permission| self.check_permission(user_id, permission))
    }

    // 清除用户权限缓存
    pub fn clear_user_cache(&mut self, user_id: &str) {
        self.permission_cache.remove(user_id);
    }

    // 清除所有权限缓存
    pub fn clear_all_cache(&mut self) {
        self.permission_cache.clear();
    }
}

// 权限检查宏
#[macro_export]
macro_rules! require_permission {
    ($permission_manager:expr, $user_id:expr, $permission:expr) => {
        if !$permission_manager.check_permission($user_id, &$permission) {
            return Err(SecurityError::PermissionDenied($permission));
        }
    };
}

#[derive(Debug, thiserror::Error)]
pub enum SecurityError {
    #[error("Permission denied: {0:?}")]
    PermissionDenied(Permission),

    #[error("Authentication required")]
    AuthenticationRequired,

    #[error("Invalid credentials")]
    InvalidCredentials,

    #[error("Session expired")]
    SessionExpired,

    #[error("Access denied")]
    AccessDenied,
}
```

**状态管理架构图：**
```
Pinia状态管理架构：

┌─────────────────────────────────────────────────────────────┐
│                        应用状态层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ ChatStore   │ │KnowledgeStore│ │ ModelStore  │ │ThemeStore│ │
│  │ 聊天状态    │ │ 知识库状态   │ │ 模型状态    │ │ 主题状态 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │NetworkStore │ │ PluginStore │ │ SystemStore │ │I18nStore │ │
│  │ 网络状态    │ │ 插件状态    │ │ 系统状态    │ │ 语言状态 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        持久化层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │LocalStorage │ │SessionStorage│ │  IndexedDB  │ │  Tauri  │ │
│  │ 用户偏好    │ │ 临时数据    │ │ 大量数据    │ │ 文件系统 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        同步机制                              │
│ • 状态变更监听 • 自动持久化 • 跨标签页同步 • 错误恢复       │
└─────────────────────────────────────────────────────────────┘
```

**ChatStore 聊天状态管理**
```typescript
// stores/chat.ts
import { defineStore } from 'pinia'
import type { Session, Message, ModelConfig } from '@/types/chat'

export const useChatStore = defineStore('chat', {
  state: () => ({
    // 会话管理
    sessions: [] as Session[],
    currentSessionId: null as string | null,

    // 消息管理
    messages: new Map<string, Message[]>(),

    // 输入状态
    inputText: '',
    isComposing: false,

    // AI状态
    isGenerating: false,
    currentModel: 'llama-2-7b-chat',
    modelConfig: {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.9,
      frequencyPenalty: 0,
      presencePenalty: 0
    } as ModelConfig,

    // UI状态
    sidebarCollapsed: false,
    messageListScrollPosition: 0,

    // 错误状态
    lastError: null as string | null,
    connectionStatus: 'connected' as 'connected' | 'disconnected' | 'reconnecting'
  }),

  getters: {
    // 当前会话
    currentSession: (state) => {
      return state.sessions.find(s => s.id === state.currentSessionId)
    },

    // 当前会话消息
    currentMessages: (state) => {
      if (!state.currentSessionId) return []
      return state.messages.get(state.currentSessionId) || []
    },

    // 会话统计
    sessionStats: (state) => {
      return state.sessions.map(session => ({
        id: session.id,
        messageCount: state.messages.get(session.id)?.length || 0,
        lastMessageTime: session.updatedAt
      }))
    },

    // 是否可以发送消息
    canSendMessage: (state) => {
      return !state.isGenerating &&
             state.inputText.trim().length > 0 &&
             state.connectionStatus === 'connected'
    }
  },

  actions: {
    // 创建新会话
    async createSession(title?: string, modelConfig?: Partial<ModelConfig>) {
      const session: Session = {
        id: generateId(),
        title: title || `新对话 ${this.sessions.length + 1}`,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        messageCount: 0,
        model: this.currentModel,
        settings: { ...this.modelConfig, ...modelConfig },
        tags: [],
        isArchived: false
      }

      this.sessions.unshift(session)
      this.messages.set(session.id, [])
      this.currentSessionId = session.id

      // 持久化
      await this.persistSessions()
    },

    // 发送消息
    async sendMessage(content: string, attachments?: File[]) {
      if (!this.currentSessionId || !this.canSendMessage) return

      const userMessage: Message = {
        id: generateId(),
        sessionId: this.currentSessionId,
        content,
        role: 'user',
        timestamp: Date.now(),
        status: 'sent',
        metadata: { attachments: attachments?.map(f => ({ name: f.name, size: f.size })) }
      }

      // 添加用户消息
      this.addMessage(userMessage)

      // 开始AI生成
      this.isGenerating = true
      this.inputText = ''

      try {
        // 调用AI推理
        await this.generateAIResponse(content, attachments)
      } catch (error) {
        this.lastError = error.message
        this.isGenerating = false
      }
    },

    // 添加消息
    addMessage(message: Message) {
      const sessionMessages = this.messages.get(message.sessionId) || []
      sessionMessages.push(message)
      this.messages.set(message.sessionId, sessionMessages)

      // 更新会话时间
      const session = this.sessions.find(s => s.id === message.sessionId)
      if (session) {
        session.updatedAt = Date.now()
        session.messageCount = sessionMessages.length
      }
    },

    // 持久化会话
    async persistSessions() {
      try {
        await invoke('save_sessions', {
          sessions: this.sessions,
          messages: Object.fromEntries(this.messages)
        })
      } catch (error) {
        console.error('Failed to persist sessions:', error)
      }
    }
  }
})
```

